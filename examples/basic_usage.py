# examples/basic_usage.py
import logging
import sys
import os
from pathlib import Path

# Adjust path to import from src
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.blog_workflow import BlogWorkflowManager, WorkflowConfig, create_persistent_workflow_manager

def setup_logging():
    """Configura logging para o exemplo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def print_workflow_summary(result: dict, workflow_manager: BlogWorkflowManager, session_id: str):
    """Imprime resumo do workflow executado"""
    print("\n" + "="*80)
    print("📊 RESUMO DO WORKFLOW")
    print("="*80)
    
    if result.get("error_message"):
        print(f"❌ ERRO: {result['error_message']}")
        print(f"📍 Última etapa: {result.get('current_step', 'desconhecida')}")
        return
    
    # Informações básicas
    print(f"📝 Tópico: {result.get('topic', 'N/A')}")
    print(f"🔄 Revisões realizadas: {result.get('revision_count', 0)}")
    print(f"✅ Status de aprovação: {'Aprovado' if result.get('human_approved') else 'Não aprovado'}")
    print(f"📈 Etapa final: {result.get('current_step', 'N/A')}")
    
    # Métricas de conteúdo
    if result.get('content_length'):
        print(f"📏 Tamanho do conteúdo: {result['content_length']} caracteres")
    
    if result.get('estimated_reading_time'):
        print(f"⏱️ Tempo de leitura estimado: {result['estimated_reading_time']} minutos")
    
    # Informações de SEO
    seo_suggestions = result.get('seo_suggestions')
    if seo_suggestions and seo_suggestions != "N/A":
        print("🔍 Otimizações de SEO: Aplicadas")
    
    # Metadados do workflow
    metadata = result.get('workflow_metadata', {})
    if metadata.get('steps_completed'):
        print(f"🎯 Etapas completadas: {len(metadata['steps_completed'])}")
        print(f"📋 Etapas: {' → '.join(metadata['steps_completed'])}")
    
    # Verifica se o post final foi gerado
    final_post = result.get('final_post', '')
    if final_post and final_post != "N/A - Conteúdo não aprovado":
        print(f"📄 Post final gerado com sucesso!")
        print(f"📊 Tamanho do post final: {len(final_post)} caracteres")
        
        # Opção de visualizar o post
        show_content = input("\n🔍 Deseja visualizar o post final? (s/n): ").strip().lower()
        if show_content in ['s', 'sim', 'y', 'yes']:
            print("\n" + "-"*60)
            print("📄 CONTEÚDO DO POST FINAL")
            print("-"*60)
            print(final_post)
            print("-"*60)
    
    # Status final detalhado
    final_status = workflow_manager.get_workflow_status(session_id)
    print(f"\n📊 Status detalhado: {final_status}")

def interactive_demo():
    """Demonstração interativa do workflow"""
    print("🚀 SISTEMA DE CRIAÇÃO DE BLOG POSTS - Demo Interativo")
    print("=" * 60)
    
    # Configuração personalizada
    print("\n🔧 Configuração do Workflow:")
    
    # Pergunta sobre persistência
    use_persistent = input("Usar banco de dados persistente? (s/n) [n]: ").strip().lower()
    
    if use_persistent in ['s', 'sim', 'y', 'yes']:
        db_path = input("Caminho do banco de dados [./workflow_demo.db]: ").strip() or "./workflow_demo.db"
        workflow_manager = create_persistent_workflow_manager(db_path)
        print(f"💾 Usando banco persistente: {db_path}")
    else:
        # Configuração em memória
        config = WorkflowConfig(
            max_revisions=3,
            memory_path=":memory:",
            content_preview_length=500
        )
        workflow_manager = BlogWorkflowManager(config)
        print("🧠 Usando banco em memória")
    
    # Configuração da sessão
    session_id = input("ID da sessão [demo_session]: ").strip() or "demo_session"
    
    # Verifica se existe sessão anterior
    initial_status = workflow_manager.get_workflow_status(session_id)
    if initial_status.current_step != "not_started":
        print(f"\n🔄 Sessão existente encontrada!")
        print(f"📍 Etapa atual: {initial_status.current_step}")
        print(f"📝 Tópico: {initial_status.topic}")
        
        continue_session = input("Continuar sessão existente? (s/n) [s]: ").strip().lower()
        if continue_session not in ['n', 'nao', 'no']:
            print("▶️ Continuando sessão existente...")
        else:
            # Reset da sessão (se implementado)
            print("🔄 Iniciando nova sessão...")
            session_id = f"{session_id}_{int(datetime.now().timestamp())}"
    
    return workflow_manager, session_id

def main():
    """Função principal melhorada"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Demo interativo ou modo simples
        demo_mode = input("Executar demo interativo? (s/n) [n]: ").strip().lower()
        
        if demo_mode in ['s', 'sim', 'y', 'yes']:
            workflow_manager, session_id = interactive_demo()
        else:
            # Modo simples - configuração padrão
            print("🚀 SISTEMA DE CRIAÇÃO DE BLOG POSTS - Modo Simples")
            print("=" * 50)
            
            config = WorkflowConfig(max_revisions=3)
            workflow_manager = BlogWorkflowManager(config)
            session_id = "simple_demo"
        
        logger.info(f"🎬 Iniciando workflow para sessão: {session_id}")
        
        # Executa o workflow
        print(f"\n▶️ Executando workflow...")
        result = workflow_manager.run_workflow(session_id)
        
        # Mostra resultado
        print_workflow_summary(result, workflow_manager, session_id)
        
        # Opções pós-execução
        if not result.get("error_message"):
            print("\n🔧 Opções adicionais:")
            print("1. Ver status detalhado")
            print("2. Executar nova sessão")
            print("3. Sair")
            
            choice = input("\nEscolha uma opção [3]: ").strip() or "3"
            
            if choice == "1":
                status = workflow_manager.get_workflow_status(session_id)
                print(f"\n📊 Status detalhado:\n{status}")
            elif choice == "2":
                print("\n🔄 Para nova sessão, execute o script novamente.")
        
        print("\n✅ Demo concluída!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Execução interrompida pelo usuário")
        logger.info("Execução interrompida por KeyboardInterrupt")
    except Exception as e:
        logger.error(f"Erro inesperado na execução: {e}", exc_info=True)
        print(f"\n❌ Erro inesperado: {e}")
    finally:
        print("👋 Obrigado por usar o Blog Workflow System!")

if __name__ == "__main__":
    # Import necessário para datetime se usado
    from datetime import datetime
    main()
