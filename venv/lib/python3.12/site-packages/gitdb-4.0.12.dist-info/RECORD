gitdb-4.0.12.dist-info/AUTHORS,sha256=aUmmuuKGJrGDzN5i-dDIbj00R1IOPcFTZDWznhEwZuM,66
gitdb-4.0.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gitdb-4.0.12.dist-info/LICENSE,sha256=79KfWWoI6IV-aOdpSlC82nKDl5LafD8EG8v_XxgAkjk,1984
gitdb-4.0.12.dist-info/METADATA,sha256=lnDS3IZgOda3yR9SzYHH8iTNdGf3aFx8KebEaR0b89k,1230
gitdb-4.0.12.dist-info/RECORD,,
gitdb-4.0.12.dist-info/WHEEL,sha256=PZUExdf71Ui_so67QXpySuHtCi3-J3wvF4ORK6k_S8U,91
gitdb-4.0.12.dist-info/top_level.txt,sha256=ss6atT8cG4mQuAYXO6PokJ0r4Mm5cBiDbKsu2e3YHfs,6
gitdb/__init__.py,sha256=Zfj1Uasvn9KgccHILrsHFZGTGFm4mvmEmw0trh6HOrM,551
gitdb/__pycache__/__init__.cpython-312.pyc,,
gitdb/__pycache__/base.cpython-312.pyc,,
gitdb/__pycache__/const.cpython-312.pyc,,
gitdb/__pycache__/exc.cpython-312.pyc,,
gitdb/__pycache__/fun.cpython-312.pyc,,
gitdb/__pycache__/pack.cpython-312.pyc,,
gitdb/__pycache__/stream.cpython-312.pyc,,
gitdb/__pycache__/typ.cpython-312.pyc,,
gitdb/__pycache__/util.cpython-312.pyc,,
gitdb/base.py,sha256=krg61c_FKEvn4KAcX6pjH-hlpFfRCcM7mrki4Hnmxew,8023
gitdb/const.py,sha256=WWmEYKNDdm3J9fxYTFT_B6-QLDSMBClbz0LSBa1D1S8,90
gitdb/db/__init__.py,sha256=aQTZnxpfk6c76X1ubZzO9pBq4STnL6hCoGxZzXurEZ4,371
gitdb/db/__pycache__/__init__.cpython-312.pyc,,
gitdb/db/__pycache__/base.cpython-312.pyc,,
gitdb/db/__pycache__/git.cpython-312.pyc,,
gitdb/db/__pycache__/loose.cpython-312.pyc,,
gitdb/db/__pycache__/mem.cpython-312.pyc,,
gitdb/db/__pycache__/pack.cpython-312.pyc,,
gitdb/db/__pycache__/ref.cpython-312.pyc,,
gitdb/db/base.py,sha256=_qpZ1VzwpoTcU3_-IIXmwIW0p63HXrtJ5jWNgjrLjRY,9061
gitdb/db/git.py,sha256=b5O01eZsnzldruzEENvqiw8Q7Daz2iETrlAqH0aggdE,2666
gitdb/db/loose.py,sha256=z7m0Cju-knikJi5UX6KWdWAJ9OOCeOE4gfYBso4XAQA,8734
gitdb/db/mem.py,sha256=VDscunT4EtRo2GBE52lup1ki3si8enImUSKmSVq8kwc,3343
gitdb/db/pack.py,sha256=iZPDF6rQ-kCrgbdsZRA83CL-zWsoQaZxo5rcTarjAoQ,7285
gitdb/db/ref.py,sha256=rGOzF3cAOBW9i0Owz_Iw6c4K5BgbYFwk5btMaXGvCoc,2591
gitdb/exc.py,sha256=B0N6I2rctCwd0Upoq2laTigbVZNOZ38nfobRcKDBtV4,1496
gitdb/fun.py,sha256=jw3paQMgxexUn2NENFavLXfABfNvDBRfRCvv2VHkvms,23243
gitdb/pack.py,sha256=buboXaTzXKEedNX5j1zIUIPhnLmHkDRl7Rg9ftclt14,39228
gitdb/stream.py,sha256=0-BWcggWWZniMXGGaAe3MR6iH9DBt5ir9YtgE2_rKKw,27541
gitdb/test/__init__.py,sha256=4cJQwIMepzCxK0hdxDegJGxNVK4dungjbPzCUJc1ZIU,204
gitdb/test/__pycache__/__init__.cpython-312.pyc,,
gitdb/test/__pycache__/lib.cpython-312.pyc,,
gitdb/test/__pycache__/test_base.cpython-312.pyc,,
gitdb/test/__pycache__/test_example.cpython-312.pyc,,
gitdb/test/__pycache__/test_pack.cpython-312.pyc,,
gitdb/test/__pycache__/test_stream.cpython-312.pyc,,
gitdb/test/__pycache__/test_util.cpython-312.pyc,,
gitdb/test/lib.py,sha256=iXBcJ3RDAN0uj9NbyEzt90TuCsDFt3SjHA61Ly6Xyxs,5489
gitdb/test/test_base.py,sha256=6zjmON19J7LRDidlMSz2iQArONdkK6_v-DzhAQJzChg,2836
gitdb/test/test_example.py,sha256=In7WgwvrGCbxPcuLCmkVIs4kdhCJ8rtcEvp115fHeU4,1350
gitdb/test/test_pack.py,sha256=bbkupZPBFlpjBcPZTZ0OmRrTA9I3-fVdfwLtNa14_3s,9228
gitdb/test/test_stream.py,sha256=RwSB49q6JHM7EFW413CZQeGRfkK92aQ0IVfTocIa3bo,5727
gitdb/test/test_util.py,sha256=WQKQBP2uPF4wwNJFlUd9-YE2Q3CmlgpoY4MRk5G0r54,3243
gitdb/typ.py,sha256=dZlbzfy5RFNHiZHEOwVy-6T-aZ3xLv0mGaJVyxKBd0M,373
gitdb/util.py,sha256=F3bE24b2QDihz4tqc3Km7wbC6N65EjwFoao-yzrRJSY,12302
gitdb/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gitdb/utils/__pycache__/__init__.cpython-312.pyc,,
gitdb/utils/__pycache__/encoding.cpython-312.pyc,,
gitdb/utils/encoding.py,sha256=ceZZFb86LGJ71cwW6qkq_BFquAlNE7jaafNbwxYRSXk,372
