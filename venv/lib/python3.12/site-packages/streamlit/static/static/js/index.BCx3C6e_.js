import{r as l,M as u,j as d,bs as x,c8 as V}from"./index.C1z8KpLA.js";import{a as h}from"./useBasicWidgetState.zXY9CjFS.js";import"./FormClearHelper.B67tgll0.js";const U=(t,o)=>t.getStringValue(o),v=t=>t.options.length===0||u(t.default)?null:t.options[t.default],C=t=>t.rawValue??null,F=(t,o,e,a)=>{o.setStringValue(t,e.value,{fromUi:e.fromUi},a)},M=({disabled:t,element:o,widgetMgr:e,fragmentId:a})=>{const{options:n,help:c,label:i,labelVisibility:r,placeholder:g,acceptNewOptions:p}=o,[f,s]=h({getStateFromWidgetMgr:U,getDefaultStateFromProto:v,getCurrStateFromProto:C,updateWidgetMgrState:F,element:o,widgetMgr:e,fragmentId:a}),m=l.useCallback(b=>{s({value:b,fromUi:!0})},[s]),S=u(o.default)&&!t;return d(V,{label:i,labelVisibility:x(r==null?void 0:r.value),options:n,disabled:t,onChange:m,value:f,help:c,placeholder:g,clearable:S,acceptNewOptions:p})},w=l.memo(M);export{w as default};
