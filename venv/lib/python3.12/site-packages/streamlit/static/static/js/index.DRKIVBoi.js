import{n as k,r as s,bi as H,z as B,ct as W,C as j,j as r,bs as O,bG as _,bt as w,bb as G,bu as K,D as $,cu as q}from"./index.C1z8KpLA.js";import{u as A}from"./uniqueId.j-1rlNNH.js";import{u as N,a as J,b as M}from"./useOnInputChange.z04u96A8.js";import{a as Q}from"./useBasicWidgetState.zXY9CjFS.js";import{I as X}from"./InputInstructions.D-Y8geDN.js";import{I as Y}from"./input.DsCfafm0.js";import"./inputUtils.CQWz5UKz.js";import"./FormClearHelper.B67tgll0.js";import"./base-input.BoAa1U94.js";const Z=k("div",{target:"e1o1zy6o0"})("position:relative;");function tt({disabled:a,element:t,widgetMgr:i,fragmentId:d}){var I;const[l,c]=s.useState(()=>y(i,t)??null),[T,x]=H(),[o,p]=s.useState(!1),V=s.useCallback(()=>{c(t.default??null),p(!0)},[t.default]),[C,m]=Q({getStateFromWidgetMgr:y,getDefaultStateFromProto:et,getCurrStateFromProto:at,updateWidgetMgrState:st,element:t,widgetMgr:i,fragmentId:d,onFormCleared:V});N(C,l,c,o);const[F,b]=s.useState(!1),e=B(),[g]=s.useState(()=>A("text_input_")),{placeholder:z,formId:u,icon:n,maxChars:f}=t,h=s.useCallback(()=>{p(!1),m({value:l,fromUi:!0})},[l,m]),v=W({formId:u})?i.allowFormEnterToSubmit(u):o,E=F&&T>e.breakpoints.hideWidgetDetails,R=s.useCallback(()=>{o&&h(),b(!1)},[o,h]),L=s.useCallback(()=>{b(!0)},[]),P=J({formId:u,maxChars:f,setDirty:p,setUiValue:c,setValueWithSource:m}),D=M(u,h,o,i,d),S=n==null?void 0:n.startsWith(":material"),U=S?"lg":"base";return j(Z,{className:"stTextInput","data-testid":"stTextInput",ref:x,children:[r(K,{label:t.label,disabled:a,labelVisibility:O((I=t.labelVisibility)==null?void 0:I.value),htmlFor:g,children:t.help&&r(_,{children:r(w,{content:t.help,placement:G.TOP_RIGHT})})}),r(Y,{value:l??"",placeholder:z,onBlur:R,onFocus:L,onChange:P,onKeyPress:D,"aria-label":t.label,disabled:a,id:g,type:it(t),autoComplete:t.autocomplete,startEnhancer:n&&r($,{"data-testid":"stTextInputIcon",iconValue:n,size:U}),overrides:{Input:{style:{minWidth:0,"::placeholder":{opacity:"0.7"},lineHeight:e.lineHeights.inputWidget,paddingRight:e.spacing.sm,paddingLeft:e.spacing.md,paddingBottom:e.spacing.sm,paddingTop:e.spacing.sm}},Root:{props:{"data-testid":"stTextInputRootElement"},style:{height:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth,paddingLeft:n?e.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:e.iconSizes.lg,color:S?e.colors.fadedText60:"inherit"}}}}),E&&r(X,{dirty:o,value:l??"",maxLength:f,inForm:W({formId:u}),allowEnterToSubmit:v})]})}function y(a,t){return a.getStringValue(t)??null}function et(a){return a.default??null}function at(a){return a.value??null}function st(a,t,i,d){t.setStringValue(a,i.value,{fromUi:i.fromUi},d)}function it(a){return a.type===q.Type.PASSWORD?"password":"text"}const ht=s.memo(tt);export{ht as default};
