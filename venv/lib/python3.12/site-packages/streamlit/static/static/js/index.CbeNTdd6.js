import{n as T,r as l,z as M,c2 as W,c3 as C,C as I,j as r,bs as V,bG as O,bt as L,bb as N,bu as P,c1 as k,c4 as E,bZ as $,c5 as A,bD as B}from"./index.C1z8KpLA.js";import{a as D}from"./useBasicWidgetState.zXY9CjFS.js";import"./FormClearHelper.B67tgll0.js";const F=T("div",{target:"euzcfsp0"})(({theme:s})=>({"span[aria-disabled='true']":{background:s.colors.fadedText05}})),H=(s,t)=>s.getStringArrayValue(t),G=s=>s.default.map(t=>s.options[t])??null,U=s=>s.rawValues??null,j=(s,t,n,p)=>{t.setStringArrayValue(s,n.value,{fromUi:n.fromUi},p)},K=s=>{var S;const{element:t,widgetMgr:n,fragmentId:p}=s,e=M(),[i,m]=D({getStateFromWidgetMgr:H,getDefaultStateFromProto:G,getCurrStateFromProto:U,updateWidgetMgrState:j,element:t,widgetMgr:n,fragmentId:p}),h=t.maxSelections>0&&i.length>=t.maxSelections,f=l.useMemo(()=>{if(t.maxSelections===0)return"No results";if(i.length===t.maxSelections){const o=t.maxSelections!==1?"options":"option";return`You can only select up to ${t.maxSelections} ${o}. Remove an option first.`}return"No results"},[t.maxSelections,i.length]),y=l.useMemo(()=>i.map(o=>({value:o,label:o})),[i]),b=l.useCallback(o=>{var c,d;switch(o.type){case"remove":return W(i,(c=o.option)==null?void 0:c.value);case"clear":return[];case"select":return i.concat([(d=o.option)==null?void 0:d.value]);default:throw new Error(`State transition is unknown: ${o.type}`)}},[i]),v=l.useCallback(o=>{t.maxSelections&&o.type==="select"&&i.length>=t.maxSelections||m({value:b(o),fromUi:!0})},[t.maxSelections,b,m,i.length]),x=l.useCallback((o,c)=>{if(h)return[];const d=o.filter(R=>!i.includes(R.value));return C(d,c)},[h,i]),{options:u}=t;let a=s.disabled,g=t.placeholder;u.length===0&&(t.acceptNewOptions?g="Add options":(g="No options to select",a=!0));const z=u.map(o=>({label:o,value:o})),w=u.length>10;return I("div",{className:"stMultiSelect","data-testid":"stMultiSelect",children:[r(P,{label:t.label,disabled:a,labelVisibility:V((S=t.labelVisibility)==null?void 0:S.value),children:t.help&&r(O,{children:r(L,{content:t.help,placement:N.TOP_RIGHT})})}),r(F,{children:r(k,{creatable:t.acceptNewOptions??!1,options:z,labelKey:"label",valueKey:"value","aria-label":t.label,placeholder:g,type:A.select,multi:!0,onChange:v,value:y,disabled:a,size:"compact",noResultsMsg:f,filterOptions:x,closeOnSelect:!1,overrides:{Popover:{props:{overrides:{Body:{style:()=>({marginTop:e.spacing.px})}}}},SelectArrow:{component:$,props:{overrides:{Svg:{style:()=>({width:e.iconSizes.xl,height:e.iconSizes.xl})}}}},IconsContainer:{style:()=>({paddingRight:e.spacing.sm})},ControlContainer:{style:{minHeight:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth}},Placeholder:{style:()=>({flex:"inherit",color:a?e.colors.fadedText40:e.colors.fadedText60})},ValueContainer:{style:()=>({paddingLeft:e.spacing.sm,paddingTop:e.spacing.none,paddingBottom:e.spacing.none,paddingRight:e.spacing.none})},ClearIcon:{props:{overrides:{Svg:{style:{color:e.colors.darkGray,padding:e.spacing.threeXS,height:e.sizes.clearIconSize,width:e.sizes.clearIconSize,cursor:"pointer",":hover":{fill:e.colors.bodyText}}}}}},SearchIcon:{style:{color:e.colors.darkGray}},Tag:{props:{overrides:{Root:{style:{borderTopLeftRadius:e.radii.md,borderTopRightRadius:e.radii.md,borderBottomRightRadius:e.radii.md,borderBottomLeftRadius:e.radii.md,fontSize:e.fontSizes.md,paddingLeft:e.spacing.sm,marginLeft:e.spacing.none,marginRight:e.spacing.sm,height:`calc(${e.sizes.minElementHeight} - 2 * ${e.spacing.xs})`,maxWidth:`calc(100% - ${e.spacing.lg})`,cursor:"default !important"}},Action:{style:{paddingLeft:0}},ActionIcon:{props:{overrides:{Svg:{style:{width:"0.625em",height:"0.625em"}}}}}}}},MultiValue:{props:{overrides:{Root:{style:{fontSize:e.fontSizes.sm}}}}},Input:{props:{readOnly:B.isMobile&&w===!1?"readonly":null}},Dropdown:{component:E}}})})]})},_=l.memo(K);export{_ as default};
