import{r as y,E as T,_ as O,n as I,J as K,C as P,j as f,bv as Y,bb as A,bq as Z,b6 as ee,bp as W,bC as z,d as te,g as re,aM as ie,z as oe,bD as ae,bE as ne,bF as se,R as le,l as de,aA as ce,M as x,bm as ue,bl as he,bs as pe,bG as fe,bt as ge,bu as me,F as ve}from"./index.C1z8KpLA.js";import{a as L}from"./index.t--hEgTQ.js";import{F as Se}from"./FormClearHelper.B67tgll0.js";import{U as k}from"./UploadFileInfo.C-jY39rj.js";import{P as ye,S as be}from"./ProgressBar.B-kexwwD.js";var N=y.forwardRef(function(t,l){var r={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:r,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:l}),y.createElement("path",{d:"M.5 1c-.28 0-.5.23-.5.5v4c0 .28.23.5.5.5h5c.28 0 .5-.22.5-.5V4l1 1h1V2H7L6 3V1.5c0-.28-.22-.5-.5-.5h-5z"}))});N.displayName="Video";var D=y.forwardRef(function(t,l){var r={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:r,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:l}),y.createElement("path",{d:"M1.41 0L0 1.41l.72.72L2.5 3.94.72 5.72 0 6.41l1.41 1.44.72-.72 1.81-1.81 1.78 1.81.69.72 1.44-1.44-.72-.69-1.81-1.78 1.81-1.81.72-.72L6.41 0l-.69.72L3.94 2.5 2.13.72 1.41 0z"}))});D.displayName="X";var H=y.forwardRef(function(t,l){var r={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return y.createElement(T,O({iconAttrs:r,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:l}),y.createElement("rect",{width:24,height:24,fill:"none"}),y.createElement("path",{d:"M20 5h-3.17l-1.24-1.35A1.99 1.99 0 0014.12 3H9.88c-.56 0-1.1.24-1.48.65L7.17 5H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1.35 8.35l-2.79 2.79c-.32.32-.86.1-.86-.35v-1.75H9v1.75c0 .45-.54.67-.85.35l-2.79-2.79c-.2-.2-.2-.51 0-.71l2.79-2.79a.5.5 0 01.85.36v1.83h6v-1.83c0-.45.54-.67.85-.35l2.79 2.79c.2.19.2.51.01.7z"}))});H.displayName="SwitchCamera";var j;(function(t){t.XSMALL="xsmall",t.SMALL="small",t.MEDIUM="medium",t.LARGE="large"})(j||(j={}));function we(t,l){switch(t){case"xsmall":return{padding:`${l.spacing.twoXS} ${l.spacing.sm}`,fontSize:l.fontSizes.sm};case"small":return{padding:`${l.spacing.twoXS} ${l.spacing.md}`};case"large":return{padding:`${l.spacing.md} ${l.spacing.md}`};default:return{padding:`${l.spacing.xs} ${l.spacing.md}`}}}const G=I("div",{target:"e14dwhi20"})({position:"relative",overflow:"hidden",width:"100%",objectFit:"contain"}),V=I("div",{target:"e14dwhi21"})(({theme:t,width:l})=>({backgroundColor:t.colors.secondaryBg,borderRadius:`${t.radii.default} ${t.radii.default} 0 0`,width:"100%",height:l*9/16,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"})),Me=I("p",{target:"e14dwhi22"})(({theme:t})=>({marginTop:t.spacing.sm,textAlign:"center"})),Ce=I("img",{target:"e14dwhi23"})(({theme:t,opacity:l})=>({borderRadius:`${t.radii.default} ${t.radii.default} 0 0`,objectFit:"contain",opacity:l})),Ue=I("a",{target:"e14dwhi24"})(({theme:t})=>({color:t.colors.primary,display:"block",textDecoration:"none"})),Ie=I("span",{target:"e14dwhi25"})({display:"flex",alignItems:"center"}),Ee=I("div",{target:"e14dwhi26"})(({theme:t})=>({position:"absolute",top:t.spacing.lg,right:t.spacing.lg,zIndex:t.zIndices.priority,color:t.colors.fadedText40,mixBlendMode:"difference",opacity:.6})),Fe=I("div",{target:"e14dwhi28"})({height:"fit-content",width:"100%",position:"absolute",bottom:0}),Pe=I("button",{target:"e14dwhi29"})(({theme:t})=>({position:"relative",display:"inline-flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:t.colors.lightenedBg05,border:`${t.sizes.borderWidth} solid ${t.colors.borderColor}`,borderRadius:`0 0 ${t.radii.default} ${t.radii.default}`,"&:hover":{borderColor:t.colors.primary,color:t.colors.primary},"&:active":{color:t.colors.white,borderColor:t.colors.primary,backgroundColor:t.colors.primary},"&:focus:not(:active)":{borderColor:t.colors.primary,color:t.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{color:t.colors.fadedText40,borderColor:t.colors.borderColor,backgroundColor:t.colors.lightenedBg05,cursor:"not-allowed"},fontWeight:t.fontWeights.normal,padding:`${t.spacing.xs} ${t.spacing.md}`,margin:t.spacing.none,lineHeight:t.lineHeights.base,color:"inherit",width:"100%",userSelect:"none","&:focus":{outline:"none"},"&:focus-visible":{boxShadow:`0 0 0 0.2rem ${K(t.colors.primary,.5)}`},...we("medium",t)}));function Re({disabled:t,onClick:l,children:r,progress:a}){return P(Pe,{disabled:t||!1,onClick:l||(()=>{}),progress:a||null,"data-testid":"stCameraInputButton",children:[r,a?f(Fe,{children:f(ye,{value:a,size:be.EXTRASMALL,overrides:{Bar:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}},BarProgress:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}},BarContainer:{style:{borderTopLeftRadius:0,borderTopRightRadius:0}}}})}):null]})}const q=y.memo(Re);var F;(function(t){t.USER="user",t.ENVIRONMENT="environment"})(F||(F={}));const xe=({switchFacingMode:t})=>f(Ee,{"data-testid":"stCameraInputSwitchButton",children:f(Y,{content:"Switch camera",placement:A.TOP_RIGHT,children:f(Z,{kind:ee.MINIMAL,onClick:t,children:f(W,{content:H,size:"twoXL",color:z.white})})})}),_e=y.memo(xe);var _={exports:{}},Te=_.exports,B;function Oe(){return B||(B=1,function(t,l){(function(a,o){t.exports=o(te())})(Te,function(r){return function(a){var o={};function n(s){if(o[s])return o[s].exports;var h=o[s]={i:s,l:!1,exports:{}};return a[s].call(h.exports,h,h.exports,n),h.l=!0,h.exports}return n.m=a,n.c=o,n.d=function(s,h,g){n.o(s,h)||Object.defineProperty(s,h,{enumerable:!0,get:g})},n.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},n.t=function(s,h){if(h&1&&(s=n(s)),h&8||h&4&&typeof s=="object"&&s&&s.__esModule)return s;var g=Object.create(null);if(n.r(g),Object.defineProperty(g,"default",{enumerable:!0,value:s}),h&2&&typeof s!="string")for(var U in s)n.d(g,U,(function(C){return s[C]}).bind(null,U));return g},n.n=function(s){var h=s&&s.__esModule?function(){return s.default}:function(){return s};return n.d(h,"a",h),h},n.o=function(s,h){return Object.prototype.hasOwnProperty.call(s,h)},n.p="",n(n.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(a,o,n){n.r(o);var s=n("react"),h=function(){var m=function(u,e){return m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,d){i.__proto__=d}||function(i,d){for(var c in d)d.hasOwnProperty(c)&&(i[c]=d[c])},m(u,e)};return function(u,e){m(u,e);function i(){this.constructor=u}u.prototype=e===null?Object.create(e):(i.prototype=e.prototype,new i)}}(),g=function(){return g=Object.assign||function(m){for(var u,e=1,i=arguments.length;e<i;e++){u=arguments[e];for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(m[d]=u[d])}return m},g.apply(this,arguments)},U=function(m,u){var e={};for(var i in m)Object.prototype.hasOwnProperty.call(m,i)&&u.indexOf(i)<0&&(e[i]=m[i]);if(m!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,i=Object.getOwnPropertySymbols(m);d<i.length;d++)u.indexOf(i[d])<0&&Object.prototype.propertyIsEnumerable.call(m,i[d])&&(e[i[d]]=m[i[d]]);return e};(function(){typeof window>"u"||(navigator.mediaDevices===void 0&&(navigator.mediaDevices={}),navigator.mediaDevices.getUserMedia===void 0&&(navigator.mediaDevices.getUserMedia=function(u){var e=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return e?new Promise(function(i,d){e.call(navigator,u,i,d)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}))})();function C(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}var E=function(m){h(u,m);function u(e){var i=m.call(this,e)||this;return i.canvas=null,i.ctx=null,i.requestUserMediaId=0,i.unmounted=!1,i.state={hasUserMedia:!1},i}return u.prototype.componentDidMount=function(){var e=this,i=e.state,d=e.props;if(this.unmounted=!1,!C()){d.onUserMediaError("getUserMedia not supported");return}i.hasUserMedia||this.requestUserMedia(),d.children&&typeof d.children!="function"&&console.warn("children must be a function")},u.prototype.componentDidUpdate=function(e){var i=this.props;if(!C()){i.onUserMediaError("getUserMedia not supported");return}var d=JSON.stringify(e.audioConstraints)!==JSON.stringify(i.audioConstraints),c=JSON.stringify(e.videoConstraints)!==JSON.stringify(i.videoConstraints),w=e.minScreenshotWidth!==i.minScreenshotWidth,S=e.minScreenshotHeight!==i.minScreenshotHeight;(c||w||S)&&(this.canvas=null,this.ctx=null),(d||c)&&(this.stopAndCleanup(),this.requestUserMedia())},u.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},u.stopMediaStream=function(e){e&&(e.getVideoTracks&&e.getAudioTracks?(e.getVideoTracks().map(function(i){e.removeTrack(i),i.stop()}),e.getAudioTracks().map(function(i){e.removeTrack(i),i.stop()})):e.stop())},u.prototype.stopAndCleanup=function(){var e=this.state;e.hasUserMedia&&(u.stopMediaStream(this.stream),e.src&&window.URL.revokeObjectURL(e.src))},u.prototype.getScreenshot=function(e){var i=this,d=i.state,c=i.props;if(!d.hasUserMedia)return null;var w=this.getCanvas(e);return w&&w.toDataURL(c.screenshotFormat,c.screenshotQuality)},u.prototype.getCanvas=function(e){var i=this,d=i.state,c=i.props;if(!this.video||!d.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var w=this.video.videoWidth,S=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var v=w/S;w=c.minScreenshotWidth||this.video.clientWidth,S=w/v,c.minScreenshotHeight&&S<c.minScreenshotHeight&&(S=c.minScreenshotHeight,w=S*v)}this.canvas=document.createElement("canvas"),this.canvas.width=(e==null?void 0:e.width)||w,this.canvas.height=(e==null?void 0:e.height)||S,this.ctx=this.canvas.getContext("2d")}var M=this,b=M.ctx,p=M.canvas;return b&&p&&(p.width=(e==null?void 0:e.width)||p.width,p.height=(e==null?void 0:e.height)||p.height,c.mirrored&&(b.translate(p.width,0),b.scale(-1,1)),b.imageSmoothingEnabled=c.imageSmoothing,b.drawImage(this.video,0,0,(e==null?void 0:e.width)||p.width,(e==null?void 0:e.height)||p.height),c.mirrored&&(b.scale(-1,1),b.translate(-p.width,0))),p},u.prototype.requestUserMedia=function(){var e=this,i=this.props,d=function(S,v){var M={video:typeof v<"u"?v:!0};i.audio&&(M.audio=typeof S<"u"?S:!0),e.requestUserMediaId++;var b=e.requestUserMediaId;navigator.mediaDevices.getUserMedia(M).then(function(p){e.unmounted||b!==e.requestUserMediaId?u.stopMediaStream(p):e.handleUserMedia(null,p)}).catch(function(p){e.handleUserMedia(p)})};if("mediaDevices"in navigator)d(i.audioConstraints,i.videoConstraints);else{var c=function(S){return{optional:[{sourceId:S}]}},w=function(S){var v=S.deviceId;return typeof v=="string"?v:Array.isArray(v)&&v.length>0?v[0]:typeof v=="object"&&v.ideal?v.ideal:null};MediaStreamTrack.getSources(function(S){var v=null,M=null;S.forEach(function(R){R.kind==="audio"?v=R.id:R.kind==="video"&&(M=R.id)});var b=w(i.audioConstraints);b&&(v=b);var p=w(i.videoConstraints);p&&(M=p),d(c(v),c(M))})}},u.prototype.handleUserMedia=function(e,i){var d=this.props;if(e||!i){this.setState({hasUserMedia:!1}),d.onUserMediaError(e);return}this.stream=i;try{this.video&&(this.video.srcObject=i),this.setState({hasUserMedia:!0})}catch{this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(i)})}d.onUserMedia(i)},u.prototype.render=function(){var e=this,i=this,d=i.state,c=i.props,w=c.audio;c.forceScreenshotSourceSize;var S=c.disablePictureInPicture;c.onUserMedia,c.onUserMediaError,c.screenshotFormat,c.screenshotQuality,c.minScreenshotWidth,c.minScreenshotHeight,c.audioConstraints,c.videoConstraints,c.imageSmoothing;var v=c.mirrored,M=c.style,b=M===void 0?{}:M,p=c.children,R=U(c,["audio","forceScreenshotSourceSize","disablePictureInPicture","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style","children"]),X=v?g(g({},b),{transform:(b.transform||"")+" scaleX(-1)"}):b,J={getScreenshot:this.getScreenshot.bind(this)};return s.createElement(s.Fragment,null,s.createElement("video",g({autoPlay:!0,disablePictureInPicture:S,src:d.src,muted:!w,playsInline:!0,ref:function(Q){e.video=Q},style:X},R)),p&&p(J))},u.defaultProps={audio:!1,disablePictureInPicture:!1,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},u}(s.Component);o.default=E},react:function(a,o){a.exports=r}}).default})}(_)),_.exports}var We=Oe();const Ve=re(We);var $;(function(t){t.PENDING="pending",t.SUCCESS="success",t.ERROR="error"})($||($={}));const Le=({width:t})=>P(V,{width:t,children:[f(W,{size:"threeXL",color:z.gray60,content:N}),P(Me,{children:["This app would like to use your camera.",f(Ue,{href:ne,rel:"noopener noreferrer",target:"_blank",children:"Learn how to allow access."})]})]}),ke=({handleCapture:t,width:l,disabled:r,clearPhotoInProgress:a,setClearPhotoInProgress:o,facingMode:n,setFacingMode:s,testOverride:h})=>{const[g,U]=y.useState(h||"pending"),C=y.useRef(null),[E,m]=y.useState(l),u=y.useCallback(ie(1e3,m),[]);y.useEffect(()=>{u(l)},[l,u]);function e(){if(C.current!==null){const d=C.current.getScreenshot();t(d)}}const i=oe();return P(G,{"data-testid":"stCameraInputWebcamComponent",children:[g!=="success"&&!r&&!a?f(Le,{width:E}):ae.isMobile&&f(_e,{switchFacingMode:s}),f(V,{"data-testid":"stCameraInputWebcamStyledBox",hidden:g!=="success"&&!r&&!a,width:E,children:!r&&f(Ve,{audio:!1,ref:C,screenshotFormat:"image/jpeg",screenshotQuality:1,width:E,height:E*9/16,style:{borderRadius:`${i.radii.default} ${i.radii.default} 0 0`},onUserMediaError:()=>{U("error")},onUserMedia:()=>{U("success"),o(!1)},videoConstraints:{width:{ideal:E},facingMode:n}})}),f(q,{onClick:e,disabled:g!=="success"||r||a,children:"Take Photo"})]})},je=y.memo(ke),Be=150,$e=de.getLogger("CameraInput");class Ae extends le.PureComponent{constructor(l){super(l),this.localFileIdCounter=1,this.RESTORED_FROM_WIDGET_STRING="RESTORED_FROM_WIDGET",this.formClearHelper=new Se,this.getProgress=()=>{if(this.state.files.length>0&&this.state.files[this.state.files.length-1].status.type==="uploading")return this.state.files[this.state.files.length-1].status.progress},this.setClearPhotoInProgress=r=>{this.setState({clearPhotoInProgress:r})},this.setFacingMode=()=>{this.setState(r=>({facingMode:r.facingMode===F.USER?F.ENVIRONMENT:F.USER}))},this.handleCapture=r=>{if(r===null)return Promise.resolve();this.setState({imgSrc:r,shutter:!0,minShutterEffectPassed:!1});const a=o=>new Promise(n=>setTimeout(n,o));return ze(r,`camera-input-${new Date().toISOString().replace(/:/g,"_")}.jpg`).then(o=>this.props.uploadClient.fetchFileURLs([o]).then(n=>({file:o,fileUrls:n[0]}))).then(({file:o,fileUrls:n})=>this.uploadFile(n,o)).then(()=>a(Be)).then(()=>{this.setState((o,n)=>({imgSrc:r,shutter:o.shutter,minShutterEffectPassed:!0}))}).catch(o=>{$e.error(o)})},this.removeCapture=()=>{this.state.files.length!==0&&(this.state.files.forEach(r=>this.deleteFile(r.id)),this.setState({imgSrc:null,clearPhotoInProgress:!0}))},this.componentDidUpdate=()=>{if(this.status!=="ready")return;const r=this.createWidgetValue(),{element:a,widgetMgr:o,fragmentId:n}=this.props,s=o.getFileUploaderStateValue(a);ce(r,s)||o.setFileUploaderStateValue(a,r,{fromUi:!0},n)},this.onFormCleared=()=>{this.setState({files:[]},()=>{const r=this.createWidgetValue();if(x(r))return;this.setState({imgSrc:null});const{widgetMgr:a,element:o,fragmentId:n}=this.props;a.setFileUploaderStateValue(o,r,{fromUi:!0},n)})},this.deleteFile=r=>{const a=this.getFile(r);x(a)||(a.status.type==="uploading"&&a.status.cancelToken.cancel(),a.status.type==="uploaded"&&a.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(a.status.fileUrls.deleteUrl),this.removeFile(r))},this.addFile=r=>{this.setState(a=>({files:[...a.files,r]}))},this.removeFile=r=>{this.setState(a=>({files:a.files.filter(o=>o.id!==r)}))},this.getFile=r=>this.state.files.find(a=>a.id===r),this.updateFile=(r,a)=>{this.setState(o=>({files:o.files.map(n=>n.id===r?a:n)}))},this.onUploadComplete=(r,a)=>{this.setState(()=>({shutter:!1}));const o=this.getFile(r);x(o)||o.status.type!=="uploading"||this.updateFile(o.id,o.setStatus({type:"uploaded",fileId:a.fileId,fileUrls:a}))},this.onUploadProgress=(r,a)=>{const o=this.getFile(a);if(x(o)||o.status.type!=="uploading")return;const n=Math.round(r.loaded*100/r.total);o.status.progress!==n&&this.updateFile(a,o.setStatus({type:"uploading",cancelToken:o.status.cancelToken,progress:n}))},this.reset=()=>{this.setState({files:[],imgSrc:null})},this.uploadFile=(r,a)=>{const o=L.CancelToken.source(),n=new k(a.name,a.size,this.nextLocalFileId(),{type:"uploading",cancelToken:o,progress:1});this.addFile(n),this.props.uploadClient.uploadFile(this.props.element,r.uploadUrl,a,s=>this.onUploadProgress(s,n.id),o.token).then(()=>this.onUploadComplete(n.id,r)).catch(s=>{L.isCancel(s)||this.updateFile(n.id,n.setStatus({type:"error",errorMessage:s?s.toString():"Unknown error"}))})},this.state=this.initialValue}get initialValue(){const l={files:[],imgSrc:null,shutter:!1,minShutterEffectPassed:!0,clearPhotoInProgress:!1,facingMode:F.USER},{widgetMgr:r,element:a}=this.props,o=r.getFileUploaderStateValue(a);if(x(o))return l;const{uploadedFileInfo:n}=o;return x(n)||n.length===0?l:{files:n.map(s=>{const h=s.name,g=s.size,U=s.fileId,C=s.fileUrls;return new k(h,g,this.nextLocalFileId(),{type:"uploaded",fileId:U,fileUrls:C})}),imgSrc:n.length===0?"":this.RESTORED_FROM_WIDGET_STRING,shutter:!1,minShutterEffectPassed:!1,clearPhotoInProgress:!1,facingMode:F.USER}}componentWillUnmount(){this.formClearHelper.disconnect()}get status(){const l=r=>r.status.type==="uploading";return this.state.files.some(l)?"updating":"ready"}componentDidMount(){const l=this.createWidgetValue(),{element:r,widgetMgr:a,fragmentId:o}=this.props;a.getFileUploaderStateValue(r)===void 0&&a.setFileUploaderStateValue(r,l,{fromUi:!1},o)}createWidgetValue(){const l=this.state.files.filter(r=>r.status.type==="uploaded").map(r=>{const{name:a,size:o,status:n}=r;return new ue({fileId:n.fileId,fileUrls:n.fileUrls,name:a,size:o})});return new he({uploadedFileInfo:l})}render(){var n;const{element:l,widgetMgr:r,disabled:a,width:o}=this.props;return this.formClearHelper.manageFormClearListener(r,l.formId,this.onFormCleared),P(G,{className:"stCameraInput","data-testid":"stCameraInput",children:[f(me,{label:l.label,disabled:a,labelVisibility:pe((n=l.labelVisibility)==null?void 0:n.value),children:l.help&&f(fe,{children:f(ge,{content:l.help,placement:A.TOP_RIGHT})})}),this.state.imgSrc?P(ve,{children:[f(V,{width:o,children:this.state.imgSrc!==this.RESTORED_FROM_WIDGET_STRING&&f(Ce,{src:this.state.imgSrc,alt:"Snapshot",opacity:this.state.shutter||!this.state.minShutterEffectPassed?"50%":"100%",width:o,height:o*9/16})}),f(q,{onClick:this.removeCapture,progress:this.getProgress(),disabled:!!this.getProgress()||a,children:this.getProgress()?"Uploading...":P(Ie,{children:[f(W,{content:D,margin:"0 xs 0 0",size:"sm"})," Clear photo"]})})]}):f(je,{handleCapture:this.handleCapture,width:o,disabled:a,clearPhotoInProgress:this.state.clearPhotoInProgress,setClearPhotoInProgress:this.setClearPhotoInProgress,facingMode:this.state.facingMode,setFacingMode:this.setFacingMode,testOverride:this.props.testOverride})]})}nextLocalFileId(){return this.localFileIdCounter++}}function ze(t,l){return fetch(t).then(r=>r.arrayBuffer()).then(r=>new File([r],l,{type:"image/jpeg"}))}const Xe=se(Ae);export{Xe as default};
