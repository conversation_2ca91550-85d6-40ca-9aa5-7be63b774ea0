import{r as i,E as J,_ as Q,n as g,B as ht,z as W,j as n,bb as Z,bv as ft,C as F,bq as tt,b6 as et,bp as T,b0 as mt,bH as yt,bk as Ct,bI as I,bt as vt,F as ot,bi as It,bJ as bt,M as k,bm as xt,bl as wt}from"./index.C1z8KpLA.js";import{g as St,F as N,C as Ft,I as Et,E as Ut,a as zt,s as Tt,u as Ht,b as Bt}from"./FileHelper.D7RMkx0e.js";import{I as Dt}from"./InputInstructions.D-Y8geDN.js";import{i as Rt}from"./inputUtils.CQWz5UKz.js";import{E as Lt}from"./ErrorOutline.esm.DU9IrB3M.js";import{a as q}from"./index.t--hEgTQ.js";import{U as nt}from"./UploadFileInfo.C-jY39rj.js";import{T as kt}from"./textarea.BR8rlyih.js";import"./base-input.BoAa1U94.js";var at=i.forwardRef(function(t,e){var s={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return i.createElement(J,Q({iconAttrs:s,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5a2.5 2.5 0 015 0v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5a2.5 2.5 0 005 0V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"}))});at.displayName="AttachFile";var rt=i.forwardRef(function(t,e){var s={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return i.createElement(J,Q({iconAttrs:s,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},t,{ref:e}),i.createElement("rect",{width:24,height:24,fill:"none"}),i.createElement("path",{d:"M3 5.51v3.71c0 .46.31.86.76.97L11 12l-7.24 1.81c-.45.11-.76.51-.76.97v3.71c0 .72.73 1.2 1.39.92l15.42-6.49c.82-.34.82-1.5 0-1.84L4.39 4.58C3.73 4.31 3 4.79 3 5.51z"}))});rt.displayName="Send";const Mt=g("div",{target:"e1togvvn0"})("border:none;position:relative;display:flex;"),$t=g("div",{target:"e1togvvn1"})(({theme:t,extended:e})=>({border:`${t.sizes.borderWidth} solid`,borderColor:t.colors.widgetBorderColor??t.colors.transparent,borderRadius:t.radii.chatInput,backgroundColor:t.colors.secondaryBg,position:"relative",flexGrow:1,display:"flex",alignItems:"center",paddingLeft:t.spacing.lg,maxHeight:e?"none":t.sizes.minElementHeight,gap:t.spacing.sm,overflow:"hidden",":focus-within":{borderColor:t.colors.primary}})),Vt=g("button",{target:"e1togvvn2"})(({theme:t,disabled:e,extended:s})=>{const p=ht(t),[f,u]=p?[t.colors.gray60,t.colors.gray80]:[t.colors.gray80,t.colors.gray40];return{border:"none",backgroundColor:t.colors.transparent,borderTopRightRadius:s?"0":t.radii.chatInput,borderTopLeftRadius:s?t.radii.default:"0",borderBottomRightRadius:t.radii.chatInput,display:"inline-flex",alignItems:"center",justifyContent:"center",lineHeight:t.lineHeights.none,margin:t.spacing.none,padding:t.spacing.sm,color:e?f:u,pointerEvents:"auto","&:focus":{outline:"none"},":focus":{outline:"none"},"&:focus-visible":{backgroundColor:p?t.colors.gray10:t.colors.gray90},"&:hover":{color:t.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{backgroundColor:t.colors.transparent,borderColor:t.colors.transparent,color:t.colors.gray,cursor:"not-allowed"}}}),Nt=g("div",{target:"e1togvvn3"})(({theme:t})=>({display:"flex",alignItems:"flex-end",height:"100%",position:"absolute",right:0,marginBottom:`-${t.sizes.borderWidth}`,pointerEvents:"none"})),Wt=g("div",{target:"e1togvvn4"})(({theme:t})=>({position:"absolute",bottom:"0px",right:`calc(${t.iconSizes.xl} + 2 * ${t.spacing.sm} + ${t.spacing.sm})`})),At=g("div",{target:"e15560op0"})(({theme:t,height:e})=>({backgroundColor:t.colors.transparent,position:"absolute",left:0,bottom:0,minHeight:`max(${t.sizes.emptyDropdownHeight}, ${e})`,width:"100%",zIndex:t.zIndices.priority})),Pt=g("div",{target:"e15560op1"})(({theme:t,height:e})=>({border:`${t.sizes.borderWidth} solid`,borderColor:t.colors.primary,borderRadius:t.radii.chatInput,backgroundColor:t.colors.secondaryBg,color:t.colors.primary,display:"flex",alignItems:"center",justifyContent:"center",height:e,width:"100%",fontWeight:t.fontWeights.bold})),Kt=g("div",{target:"e15560op2"})(({theme:t,disabled:e})=>({display:"flex",alignItems:"top",height:"100%",marginTop:`-${t.sizes.borderWidth}`,cursor:e?"not-allowed":"auto"})),Ot=g("div",{target:"e15560op3"})(({disabled:t})=>({pointerEvents:t?"none":"auto"})),_t=g("div",{target:"e15560op4"})(({theme:t})=>({marginTop:"0.625em",marginLeft:t.spacing.sm,height:t.spacing.xl,width:t.sizes.borderWidth,backgroundColor:t.colors.fadedText20})),Xt=g("div",{target:"e15560op5"})(({theme:t})=>({left:0,right:0,lineHeight:t.lineHeights.tight,paddingLeft:t.spacing.sm,paddingRight:t.spacing.sm,overflowX:"auto"})),Gt=g("div",{target:"e15560op6"})({display:"flex"}),jt=g("div",{target:"e15560op7"})({flex:"0 0 auto"}),Yt=g("div",{target:"e15560op8"})(({theme:t})=>({display:"flex",alignItems:"center",padding:t.spacing.sm,gap:t.spacing.twoXS})),qt=g("div",{target:"e15560op9"})(({theme:t})=>({color:t.colors.fadedText60})),Jt=g("div",{target:"e15560op10"})(({theme:t,fileStatus:e})=>({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:e.type==="uploaded"?t.colors.bodyText:t.colors.fadedText60})),Qt=g("div",{target:"e15560op11"})(({theme:t})=>({marginRight:t.spacing.md,color:t.colors.fadedText60})),Zt=g("small",{target:"e15560op12"})(({theme:t})=>({display:"flex",alignItems:"center",maxHeight:t.sizes.smallElementHeight,color:t.colors.fadedText60,"& :hover":{color:t.colors.bodyText}}));function te({children:t,content:e}){const s=W();return n(ft,{content:e,placement:Z.TOP,overrides:{Body:{style:{top:`-${s.sizes.minElementHeight}`}}},children:t})}const ee=({fileInfo:t})=>{const e=W(),{type:s}=t.status;switch(s){case"uploading":return n(yt,{usingCustomTheme:!1,"data-testid":"stChatInputFileIconSpinner",size:"lg",margin:"0",padding:"0"});case"error":return n(te,{content:t.status.errorMessage,children:n(T,{color:e.colors.red,content:Lt,size:"lg"})});case"uploaded":return n(T,{content:Et,size:"lg"});default:return mt(s),null}},oe=({fileInfo:t,onDelete:e})=>F(Yt,{className:"stChatInputFile","data-testid":"stChatInputFile",children:[n(qt,{children:n(ee,{fileInfo:t})}),n(Jt,{className:"stChatInputFileName","data-testid":"stChatInputFileName",title:t.name,fileStatus:t.status,children:t.name}),n(Qt,{children:St(t.size,N.Byte)}),n(Zt,{"data-testid":"stChatInputDeleteBtn",children:n(tt,{onClick:()=>e(t.id),kind:et.MINIMAL,children:n(T,{content:Ft,size:"lg"})})})]}),ne=i.memo(oe),ae=({items:t,onDelete:e})=>n(Xt,{"data-testid":"stChatUploadedFiles",children:n(Gt,{children:t.map(s=>n(jt,{children:n(ne,{fileInfo:s,onDelete:e})},s.id))})}),re=i.memo(ae),ie=({getNextLocalFileId:t,addFiles:e,updateFile:s,uploadClient:p,element:f,onUploadProgress:u,onUploadComplete:C})=>(b,d)=>{const m=q.CancelToken.source(),c=new nt(d.name,d.size,t(),{type:"uploading",cancelToken:m,progress:1});e([c]),p.uploadFile({formId:"",...f},b.uploadUrl,d,h=>u(h,c.id),m.token).then(()=>C(c.id,b)).catch(h=>{q.isCancel(h)||s(c.id,c.setStatus({type:"error",errorMessage:h?h.toString():"Unknown error"}))})},se=({acceptMultipleFiles:t,maxFileSize:e,uploadClient:s,uploadFile:p,addFiles:f,getNextLocalFileId:u,deleteExistingFiles:C,onUploadComplete:b})=>(d,m)=>{if(!t&&d.length===0&&m.length>1){const c=m.findIndex(h=>{var x;return((x=h.errors)==null?void 0:x[0].code)===Ut.TooManyFiles});c>=0&&(d.push(m[c].file),m.splice(c,1))}if(!t&&d.length>0&&C(),s.fetchFileURLs(d).then(c=>{Ct(c,d).forEach(([h,x])=>{p(h,x)})}).catch(c=>{f(d.map(h=>new nt(h.name,h.size,u(),{type:"error",errorMessage:c})))}),m.length>0){const c=m.map(h=>zt(h,u(),e));f(c)}b()},le=({getRootProps:t,getInputProps:e,acceptFile:s,disabled:p,theme:f})=>F(Kt,{disabled:p,children:[F(Ot,{"data-testid":"stChatInputFileUploadButton",disabled:p,...t(),children:[n("input",{...e()}),n(vt,{content:`Upload or drag and drop ${s===I.Multiple?"files":"a file"}`,placement:Z.TOP,onMouseEnterDelay:500,children:n(tt,{kind:et.MINIMAL,disabled:p,children:n(T,{content:at,size:"lg",color:p?f.colors.fadedText40:f.colors.fadedText60})})})]}),n(_t,{})]}),de=i.memo(le),ce=({getRootProps:t,getInputProps:e,acceptFile:s,inputHeight:p})=>F(ot,{children:[n(At,{height:p,...t(),children:n("input",{...e()})}),n(Pt,{height:p,children:`Drag and drop ${s===I.Multiple?"files":"a file"} here`})]}),pe=i.memo(ce),ue=6.5,M=1,$=(t,e,s)=>s.map(p=>p.id===t?e:p),V=(t,e)=>e.find(s=>s.id===t);function ge({disabled:t,element:e,widgetMgr:s,fragmentId:p,uploadClient:f}){const u=W(),{placeholder:C,maxChars:b}=e,d=i.useRef(null),m=i.useRef(0),c=i.useRef({minHeight:0,maxHeight:0}),[h,x]=It(),[E,B]=i.useState(e.default),[U,D]=i.useState(0),[H,it]=i.useState(!1),[w,S]=i.useState([]),[z,R]=i.useState(!1),L=i.useMemo(()=>w.some(o=>o.status.type==="uploading")?!1:E!==""||w.length>0,[w,E]),v=bt(e.acceptFile),A=Tt(e.maxUploadSizeMb,N.Megabyte,N.Byte),P=i.useCallback(o=>S(a=>[...a,...o]),[]),K=i.useCallback(o=>{S(a=>{const l=V(o,a);return k(l)?a:(l.status.type==="uploading"&&l.status.cancelToken.cancel(),l.status.type==="uploaded"&&l.status.fileUrls.deleteUrl&&f.deleteFile(l.status.fileUrls.deleteUrl),a.filter(r=>r.id!==o))})},[f]),st=()=>{const o=w.filter(a=>a.status.type==="uploaded").map(a=>{const{name:l,size:r,status:y}=a,{fileId:ut,fileUrls:gt}=y;return new xt({fileId:ut,fileUrls:gt,name:l,size:r})});return new wt({uploadedFileInfo:o})},O=()=>m.current++,lt=se({acceptMultipleFiles:v===I.Multiple,maxFileSize:A,uploadClient:f,uploadFile:ie({getNextLocalFileId:O,addFiles:P,updateFile:(o,a)=>{S(l=>$(o,a,l))},uploadClient:f,element:e,onUploadProgress:(o,a)=>{S(l=>{const r=V(a,l);if(k(r)||r.status.type!=="uploading")return l;const y=Math.round(o.loaded*100/o.total);return r.status.progress===y?l:$(a,r.setStatus({type:"uploading",cancelToken:r.status.cancelToken,progress:y}),l)})},onUploadComplete:(o,a)=>{S(l=>{const r=V(o,l);return k(r)||r.status.type!=="uploading"?l:$(r.id,r.setStatus({type:"uploaded",fileId:a.fileId,fileUrls:a}),l)})}}),addFiles:P,getNextLocalFileId:O,deleteExistingFiles:()=>w.forEach(o=>K(o.id)),onUploadComplete:()=>{d.current&&d.current.focus()}}),{getRootProps:_,getInputProps:X}=Ht({onDrop:lt,multiple:v===I.Multiple,accept:Bt(e.fileType),maxSize:A}),G=()=>{let o=0;const{current:a}=d;return a&&(a.style.height="auto",o=a.scrollHeight,a.style.height=""),o},j=()=>{if(d.current&&d.current.focus(),!L||t)return;const o={data:E,fileUploaderState:st()};s.setChatInputValue(e,o,{fromUi:!0},p),S([]),B(""),D(0)},dt=o=>{const{metaKey:a,ctrlKey:l,shiftKey:r}=o;Rt(o)&&!r&&!l&&!a&&(o.preventDefault(),j())},ct=o=>{const{value:a}=o.target,{maxChars:l}=e;l!==0&&a.length>l||(B(a),D(G()))};i.useEffect(()=>{if(e.setValue){e.setValue=!1;const o=e.value||"";B(o)}},[e]),i.useLayoutEffect(()=>{if(d.current){const{offsetHeight:o}=d.current;c.current.minHeight=o,c.current.maxHeight=o*ue}},[d]),i.useEffect(()=>{const o=r=>{var y;r.preventDefault(),r.stopPropagation(),!z&&((y=r.dataTransfer)!=null&&y.types.includes("Files"))&&R(!0)},a=r=>{r.preventDefault(),r.stopPropagation(),z&&(r.clientX<=0&&r.clientY<=0||r.clientX>=window.innerWidth&&r.clientY>=window.innerHeight)&&R(!1)},l=r=>{r.preventDefault(),r.stopPropagation(),z&&R(!1)};return window.addEventListener("dragover",o),window.addEventListener("drop",l),window.addEventListener("dragleave",a),()=>{window.removeEventListener("dragover",o),window.removeEventListener("drop",l),window.removeEventListener("dragleave",a)}},[z]),i.useLayoutEffect(()=>{const{minHeight:o}=c.current;it(U>0&&d.current?Math.abs(U-o)>M:!1)},[U]),i.useLayoutEffect(()=>{D(G())},[C]);const{maxHeight:Y}=c.current,pt=v!==I.None&&z;return F(ot,{children:[v===I.None?null:n(re,{items:[...w],onDelete:K}),n(Mt,{className:"stChatInput","data-testid":"stChatInput",ref:x,children:pt?n(pe,{getRootProps:_,getInputProps:X,acceptFile:v,inputHeight:H?`${U+M}px`:u.sizes.minElementHeight}):F($t,{extended:H,children:[v===I.None?null:n(de,{getRootProps:_,getInputProps:X,acceptFile:v,disabled:t,theme:u}),n(kt,{inputRef:d,value:E,placeholder:C,onChange:ct,onKeyDown:dt,"aria-label":C,disabled:t,rows:1,overrides:{Root:{style:{minHeight:u.sizes.minElementHeight,outline:"none",borderLeftWidth:"0",borderRightWidth:"0",borderTopWidth:"0",borderBottomWidth:"0",borderTopLeftRadius:"0",borderTopRightRadius:"0",borderBottomRightRadius:"0",borderBottomLeftRadius:"0"}},Input:{props:{"data-testid":"stChatInputTextArea"},style:{lineHeight:u.lineHeights.inputWidget,"::placeholder":{opacity:"0.7"},height:H?`${U+M}px`:"auto",maxHeight:Y?`${Y}px`:"none",paddingLeft:u.spacing.none,paddingBottom:u.spacing.sm,paddingTop:u.spacing.sm,paddingRight:`calc(${u.iconSizes.xl} + 2 * ${u.spacing.sm} + ${u.spacing.sm})`}}}}),h>u.breakpoints.hideWidgetDetails&&n(Wt,{children:n(Dt,{dirty:L,value:E,maxLength:b,type:"chat",inForm:!1})}),n(Nt,{children:n(Vt,{onClick:j,disabled:!L||t,extended:H,"data-testid":"stChatInputSubmitButton",children:n(T,{content:rt,size:"xl",color:"inherit"})})})]})})]})}const we=i.memo(ge);export{we as default};
