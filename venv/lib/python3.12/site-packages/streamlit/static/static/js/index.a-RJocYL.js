import{r as c,E as C,_ as b,n as d,H as X,C as g,j as a,bp as m,bq as F,b5 as O,b6 as S,cr as _,cs as G,a_ as q,F as K,bF as Y,R as J,aA as Q,bk as Z,M as h,b as f,bm as ee,bl as te,bs as ie,bG as se,bt as le,bb as ne,bu as ae}from"./index.C1z8KpLA.js";import{a as I}from"./index.t--hEgTQ.js";import{F as oe}from"./FormClearHelper.B67tgll0.js";import{g as M,F as y,b as re,D as de,I as ce,C as pe,a as ge,s as ue}from"./FileHelper.D7RMkx0e.js";import{S as he,P as me}from"./ProgressBar.B-kexwwD.js";import{u as fe}from"./Hooks.ncTJktu9.js";import{U as w}from"./UploadFileInfo.C-jY39rj.js";var D=c.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return c.createElement(C,b({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),c.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),c.createElement("path",{d:"M19.35 10.04A7.49 7.49 0 0012 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 000 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95A5.469 5.469 0 0112 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11A2.98 2.98 0 0122 15c0 1.65-1.35 3-3 3zM8 13h2.55v3h2.9v-3H16l-4-4z"}))});D.displayName="CloudUpload";var B=c.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return c.createElement(C,b({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),c.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"}))});B.displayName="Error";const V=d("section",{target:"e16xj5sw0"})(({isDisabled:e,theme:s})=>({display:"flex",alignItems:"center",padding:s.spacing.lg,backgroundColor:s.colors.secondaryBg,borderRadius:s.radii.default,border:s.colors.widgetBorderColor?`${s.sizes.borderWidth} solid ${s.colors.widgetBorderColor}`:void 0,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 1px ${s.colors.primary}`},color:e?s.colors.gray:s.colors.bodyText})),P=d("div",{target:"e16xj5sw1"})({marginRight:"auto",alignItems:"center",display:"flex"}),L=d("span",{target:"e16xj5sw2"})(({theme:e})=>({color:e.colors.darkenedBgMix100,marginRight:e.spacing.lg})),Fe=d("span",{target:"e16xj5sw3"})(({theme:e})=>({marginBottom:e.spacing.twoXS})),Se=d("div",{target:"e16xj5sw4"})({display:"flex",flexDirection:"column"}),j=d("div",{target:"e16xj5sw5"})(({theme:e})=>({left:0,right:0,lineHeight:e.lineHeights.tight,paddingTop:e.spacing.md,paddingLeft:e.spacing.lg,paddingRight:e.spacing.lg})),ye=d("ul",{target:"e16xj5sw6"})(({theme:e})=>({listStyleType:"none",margin:e.spacing.none,padding:e.spacing.none})),E=d("li",{target:"e16xj5sw7"})(({theme:e})=>({margin:e.spacing.none,padding:e.spacing.none})),A=d("div",{target:"e16xj5sw8"})(({theme:e})=>({display:"flex",alignItems:"baseline",flex:1,paddingLeft:e.spacing.lg,overflow:"hidden"})),W=d("div",{target:"e16xj5sw9"})(({theme:e})=>({marginRight:e.spacing.sm,marginBottom:e.spacing.twoXS,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"})),k=d("div",{target:"e16xj5sw10"})(({theme:e})=>({display:"flex",alignItems:"center",marginBottom:e.spacing.twoXS})),xe=d("span",{target:"e16xj5sw11"})(({theme:e})=>({marginRight:e.spacing.twoXS})),we=d("div",{target:"e16xj5sw12"})(({theme:e})=>({display:"flex",padding:e.spacing.twoXS,color:e.colors.darkenedBgMix100})),R=d("small",{target:"e16xj5sw13"})(({theme:e})=>({color:e.colors.red,fontSize:e.fontSizes.sm,height:e.fontSizes.sm,lineHeight:e.fontSizes.sm,display:"flex",alignItems:"center",whiteSpace:"nowrap"})),H=d("span",{target:"e16xj5sw14"})({}),Ue=e=>({[V]:{display:"flex",flexDirection:"column",alignItems:"flex-start"},[P]:{marginBottom:e.spacing.lg},[L]:{display:"none"},[j]:{paddingRight:e.spacing.lg},[k]:{maxWidth:"inherit",flex:1,alignItems:"flex-start",marginBottom:e.spacing.sm},[W]:{width:e.sizes.full},[A]:{flexDirection:"column"},[R]:{height:"auto",whiteSpace:"initial"},[H]:{display:"none"},[E]:{margin:e.spacing.none,padding:e.spacing.none}}),Ie=d("div",{target:"e16xj5sw15"})(({theme:e,width:s})=>{if(s<X("23rem"))return Ue(e)});var v;(function(e){e.DANGER="danger"})(v||(v={}));const U=d("small",{target:"ejh2rmr0"})(({kind:e,theme:s})=>{const{danger:t,fadedText60:i}=s.colors;return{color:e==="danger"?t:i,fontSize:s.fontSizes.sm,lineHeight:s.lineHeights.tight}}),ve=({multiple:e,acceptedExtensions:s,maxSizeBytes:t})=>g(P,{"data-testid":"stFileUploaderDropzoneInstructions",children:[a(L,{children:a(m,{content:D,size:"threeXL"})}),g(Se,{children:[g(Fe,{children:["Drag and drop file",e?"s":""," here"]}),g(U,{children:[`Limit ${M(t,y.Byte,0)} per file`,s.length?` • ${s.map(i=>i.replace(/^\./,"").toUpperCase()).join(", ")}`:null]})]})]}),ze=c.memo(ve),Ce=({onDrop:e,multiple:s,acceptedExtensions:t,maxSizeBytes:i,disabled:l,label:o})=>a(de,{onDrop:e,multiple:s,accept:re(t),maxSize:i,disabled:l,useFsAccessApi:!1,children:({getRootProps:n,getInputProps:r})=>g(V,{...n(),"data-testid":"stFileUploaderDropzone",isDisabled:l,"aria-label":o,children:[a("input",{"data-testid":"stFileUploaderDropzoneInput",...r()}),a(ze,{multiple:s,acceptedExtensions:t,maxSizeBytes:i}),a(F,{kind:S.SECONDARY,disabled:l,size:O.SMALL,children:"Browse files"})]})}),be=c.memo(Ce),Me=d("div",{target:"egc9vxm0"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"space-between",paddingBottom:e.spacing.twoXS,marginBottom:e.spacing.twoXS})),De=d("div",{target:"egc9vxm1"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",color:e.colors.fadedText40})),Be=({currentPage:e,totalPages:s,onNext:t,onPrevious:i})=>g(Me,{"data-testid":"stFileUploaderPagination",children:[a(U,{children:`Showing page ${e} of ${s}`}),g(De,{children:[a(F,{onClick:i,kind:S.MINIMAL,children:a(m,{content:_,size:"xl"})}),a(F,{onClick:t,kind:S.MINIMAL,children:a(m,{content:G,size:"xl"})})]})]}),Ve=c.memo(Be),z=(e,s)=>Math.ceil(e.length/s),Pe=e=>q(({pageSize:t,items:i,resetOnAdd:l,...o})=>{const[n,r]=c.useState(0),[p,x]=c.useState(()=>z(i,t)),u=fe(i);c.useEffect(()=>{u&&u.length!==i.length&&x(z(i,t)),u&&u.length<i.length?l&&r(0):n+1>=p&&r(p-1)},[i,n,t,u,l,p]);const N=()=>{r(Math.min(n+1,p-1))},T=()=>{r(Math.max(0,n-1))},$=i.slice(n*t,n*t+t);return g(K,{children:[a(e,{items:$,...o}),i.length>t?a(Ve,{pageSize:t,totalPages:p,currentPage:n+1,onNext:N,onPrevious:T}):null]})},e),Le=({fileInfo:e})=>e.status.type==="uploading"?a(me,{value:e.status.progress,size:he.SMALL}):e.status.type==="error"?g(R,{children:[a(xe,{"data-testid":"stFileUploaderFileErrorMessage",children:e.status.errorMessage}),a(H,{children:a(m,{content:B,size:"lg"})})]}):e.status.type==="uploaded"?a(U,{children:M(e.size,y.Byte)}):null,je=({fileInfo:e,onDelete:s})=>g(k,{className:"stFileUploaderFile","data-testid":"stFileUploaderFile",children:[a(we,{children:a(m,{content:ce,size:"twoXL"})}),g(A,{className:"stFileUploaderFileData",children:[a(W,{className:"stFileUploaderFileName","data-testid":"stFileUploaderFileName",title:e.name,children:e.name}),a(Le,{fileInfo:e})]}),a("div",{"data-testid":"stFileUploaderDeleteBtn",children:a(F,{onClick:()=>s(e.id),kind:S.MINIMAL,children:a(m,{content:pe,size:"lg"})})})]}),Ee=c.memo(je),Ae=({items:e,onDelete:s})=>a(ye,{children:e.map(t=>a(E,{children:a(Ee,{fileInfo:t,onDelete:s})},t.id))}),We=Pe(Ae),ke=e=>a(j,{children:a(We,{...e})}),Re=c.memo(ke);class He extends J.PureComponent{constructor(s){super(s),this.formClearHelper=new oe,this.localFileIdCounter=1,this.forceUpdatingStatus=!1,this.componentDidUpdate=()=>{if(this.status!=="ready")return;const t=this.createWidgetValue(),{element:i,widgetMgr:l,fragmentId:o}=this.props,n=l.getFileUploaderStateValue(i);Q(t,n)||l.setFileUploaderStateValue(i,t,{fromUi:!0},o)},this.dropHandler=(t,i)=>{const{element:l}=this.props,{multipleFiles:o}=l;if(!o&&t.length===0&&i.length>1){const n=i.findIndex(r=>r.errors.length===1&&r.errors[0].code==="too-many-files");n>=0&&(t.push(i[n].file),i.splice(n,1))}if(this.props.uploadClient.fetchFileURLs(t).then(n=>{if(!o&&t.length>0){const r=this.state.files.find(p=>p.status.type!=="error");r&&(this.forceUpdatingStatus=!0,this.deleteFile(r.id),this.forceUpdatingStatus=!1)}Z(n,t).forEach(([r,p])=>{this.uploadFile(r,p)})}).catch(n=>{this.addFiles(t.map(r=>new w(r.name,r.size,this.nextLocalFileId(),{type:"error",errorMessage:n})))}),i.length>0){const n=i.map(r=>ge(r,this.nextLocalFileId(),this.maxUploadSizeInBytes));this.addFiles(n)}},this.uploadFile=(t,i)=>{const l=I.CancelToken.source(),o=new w(i.name,i.size,this.nextLocalFileId(),{type:"uploading",cancelToken:l,progress:1});this.addFile(o),this.props.uploadClient.uploadFile(this.props.element,t.uploadUrl,i,n=>this.onUploadProgress(n,o.id),l.token).then(()=>this.onUploadComplete(o.id,t)).catch(n=>{I.isCancel(n)||this.updateFile(o.id,o.setStatus({type:"error",errorMessage:n?n.toString():"Unknown error"}))})},this.onUploadComplete=(t,i)=>{const l=this.getFile(t);h(l)||l.status.type!=="uploading"||this.updateFile(l.id,l.setStatus({type:"uploaded",fileId:i.fileId,fileUrls:i}))},this.deleteFile=t=>{const i=this.getFile(t);h(i)||(i.status.type==="uploading"&&i.status.cancelToken.cancel(),i.status.type==="uploaded"&&i.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(i.status.fileUrls.deleteUrl),this.removeFile(t))},this.addFile=t=>{f.flushSync(()=>{this.setState(i=>({files:[...i.files,t]}))})},this.addFiles=t=>{f.flushSync(()=>{this.setState(i=>({files:[...i.files,...t]}))})},this.removeFile=t=>{f.flushSync(()=>{this.setState(i=>({files:i.files.filter(l=>l.id!==t)}))})},this.getFile=t=>this.state.files.find(i=>i.id===t),this.updateFile=(t,i)=>{f.flushSync(()=>{this.setState(l=>({files:l.files.map(o=>o.id===t?i:o)}))})},this.onUploadProgress=(t,i)=>{const l=this.getFile(i);if(h(l)||l.status.type!=="uploading")return;const o=Math.round(t.loaded*100/t.total);l.status.progress!==o&&this.updateFile(i,l.setStatus({type:"uploading",cancelToken:l.status.cancelToken,progress:o}))},this.onFormCleared=()=>{f.flushSync(()=>{this.setState({files:[]},()=>{const t=this.createWidgetValue();if(h(t))return;const{widgetMgr:i,element:l,fragmentId:o}=this.props;i.setFileUploaderStateValue(l,t,{fromUi:!0},o)})})},this.state=this.initialValue}get initialValue(){const s={files:[]},{widgetMgr:t,element:i}=this.props,l=t.getFileUploaderStateValue(i);if(h(l))return s;const{uploadedFileInfo:o}=l;return h(o)||o.length===0?s:{files:o.map(n=>{const r=n.name,p=n.size,x=n.fileId,u=n.fileUrls;return new w(r,p,this.nextLocalFileId(),{type:"uploaded",fileId:x,fileUrls:u})})}}componentWillUnmount(){this.formClearHelper.disconnect()}get maxUploadSizeInBytes(){const s=this.props.element.maxUploadSizeMb;return ue(s,y.Megabyte,y.Byte)}get status(){const s=t=>t.status.type==="uploading";return this.state.files.some(s)||this.forceUpdatingStatus?"updating":"ready"}componentDidMount(){const s=this.createWidgetValue(),{element:t,widgetMgr:i,fragmentId:l}=this.props;i.getFileUploaderStateValue(t)===void 0&&i.setFileUploaderStateValue(t,s,{fromUi:!1},l)}createWidgetValue(){const s=this.state.files.filter(t=>t.status.type==="uploaded").map(t=>{const{name:i,size:l,status:o}=t,{fileId:n,fileUrls:r}=o;return new ee({fileId:n,fileUrls:r,name:i,size:l})});return new te({uploadedFileInfo:s})}render(){var p;const{files:s}=this.state,{element:t,disabled:i,widgetMgr:l,width:o}=this.props,n=t.type;this.formClearHelper.manageFormClearListener(l,t.formId,this.onFormCleared);const r=s.slice().reverse();return g(Ie,{className:"stFileUploader","data-testid":"stFileUploader",width:o,children:[a(ae,{label:t.label,disabled:i,labelVisibility:ie((p=t.labelVisibility)==null?void 0:p.value),children:t.help&&a(se,{children:a(le,{content:t.help,placement:ne.TOP_RIGHT})})}),a(be,{onDrop:this.dropHandler,multiple:t.multipleFiles,acceptedExtensions:n,maxSizeBytes:this.maxUploadSizeInBytes,label:t.label,disabled:i}),r.length>0&&a(Re,{items:r,pageSize:3,onDelete:this.deleteFile,resetOnAdd:!0})]})}nextLocalFileId(){return this.localFileIdCounter++}}const qe=Y(c.memo(He));export{qe as default};
