"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Markdown(google.protobuf.message.Message):
    """Formatted text"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Type:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _TypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Markdown._Type.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSPECIFIED: Markdown._Type.ValueType  # 0
        """This is recommended to be reserved for proto files backwards compatibility reasons."""
        NATIVE: Markdown._Type.ValueType  # 1
        CAPTION: Markdown._Type.ValueType  # 2
        CODE: Markdown._Type.ValueType  # 3
        LATEX: Markdown._Type.ValueType  # 4
        DIVIDER: Markdown._Type.ValueType  # 5

    class Type(_Type, metaclass=_TypeEnumTypeWrapper): ...
    UNSPECIFIED: Markdown.Type.ValueType  # 0
    """This is recommended to be reserved for proto files backwards compatibility reasons."""
    NATIVE: Markdown.Type.ValueType  # 1
    CAPTION: Markdown.Type.ValueType  # 2
    CODE: Markdown.Type.ValueType  # 3
    LATEX: Markdown.Type.ValueType  # 4
    DIVIDER: Markdown.Type.ValueType  # 5

    BODY_FIELD_NUMBER: builtins.int
    ALLOW_HTML_FIELD_NUMBER: builtins.int
    IS_CAPTION_FIELD_NUMBER: builtins.int
    ELEMENT_TYPE_FIELD_NUMBER: builtins.int
    HELP_FIELD_NUMBER: builtins.int
    body: builtins.str
    """Content to display."""
    allow_html: builtins.bool
    is_caption: builtins.bool
    """TODO [Karen]: Remove this field if favor of element_type"""
    element_type: global___Markdown.Type.ValueType
    help: builtins.str
    def __init__(
        self,
        *,
        body: builtins.str = ...,
        allow_html: builtins.bool = ...,
        is_caption: builtins.bool = ...,
        element_type: global___Markdown.Type.ValueType = ...,
        help: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["allow_html", b"allow_html", "body", b"body", "element_type", b"element_type", "help", b"help", "is_caption", b"is_caption"]) -> None: ...

global___Markdown = Markdown
