# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Skeleton.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1estreamlit/proto/Skeleton.proto\"y\n\x08Skeleton\x12&\n\x05style\x18\x01 \x01(\x0e\x32\x17.Skeleton.SkeletonStyle\x12\x13\n\x06height\x18\x02 \x01(\x05H\x00\x88\x01\x01\"%\n\rSkeletonStyle\x12\x0b\n\x07\x45LEMENT\x10\x00\x12\x07\n\x03\x41PP\x10\x01\x42\t\n\x07_heightB-\n\x1c\x63om.snowflake.apps.streamlitB\rSkeletonProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Skeleton_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\rSkeletonProto'
  _globals['_SKELETON']._serialized_start=34
  _globals['_SKELETON']._serialized_end=155
  _globals['_SKELETON_SKELETONSTYLE']._serialized_start=107
  _globals['_SKELETON_SKELETONSTYLE']._serialized_end=144
# @@protoc_insertion_point(module_scope)
