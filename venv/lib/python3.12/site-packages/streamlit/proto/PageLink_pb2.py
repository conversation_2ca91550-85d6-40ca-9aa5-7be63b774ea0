# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/PageLink.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1estreamlit/proto/PageLink.proto\"\xbb\x01\n\x08PageLink\x12\x0c\n\x04page\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x0c\n\x04icon\x18\x03 \x01(\t\x12\x18\n\x10page_script_hash\x18\x04 \x01(\t\x12\x0c\n\x04help\x18\x05 \x01(\t\x12 \n\x13use_container_width\x18\x06 \x01(\x08H\x00\x88\x01\x01\x12\x10\n\x08\x64isabled\x18\x07 \x01(\x08\x12\x10\n\x08\x65xternal\x18\x08 \x01(\x08\x42\x16\n\x14_use_container_widthB-\n\x1c\x63om.snowflake.apps.streamlitB\rPageLinkProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.PageLink_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\rPageLinkProto'
  _globals['_PAGELINK']._serialized_start=35
  _globals['_PAGELINK']._serialized_end=222
# @@protoc_insertion_point(module_scope)
