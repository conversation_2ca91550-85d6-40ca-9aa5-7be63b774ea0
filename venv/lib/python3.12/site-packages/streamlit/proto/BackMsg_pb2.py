# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/BackMsg.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import ClientState_pb2 as streamlit_dot_proto_dot_ClientState__pb2
from streamlit.proto import Common_pb2 as streamlit_dot_proto_dot_Common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dstreamlit/proto/BackMsg.proto\x1a!streamlit/proto/ClientState.proto\x1a\x1cstreamlit/proto/Common.proto\"\xf2\x02\n\x07\x42\x61\x63kMsg\x12\x15\n\x0b\x63lear_cache\x18\x05 \x01(\x08H\x00\x12\x19\n\x0fset_run_on_save\x18\x06 \x01(\x08H\x00\x12\x15\n\x0bstop_script\x18\x07 \x01(\x08H\x00\x12$\n\x0crerun_script\x18\x0b \x01(\x0b\x32\x0c.ClientStateH\x00\x12\x17\n\rload_git_info\x18\x0c \x01(\x08H\x00\x12$\n\x1a\x64\x65\x62ug_disconnect_websocket\x18\x0e \x01(\x08H\x00\x12 \n\x16\x64\x65\x62ug_shutdown_runtime\x18\x0f \x01(\x08H\x00\x12-\n\x11\x66ile_urls_request\x18\x10 \x01(\x0b\x32\x10.FileURLsRequestH\x00\x12\x17\n\rapp_heartbeat\x18\x11 \x01(\x08H\x00\x12\x1d\n\x15\x64\x65\x62ug_last_backmsg_id\x18\r \x01(\tB\x06\n\x04typeJ\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x08\x10\tJ\x04\x08\t\x10\nJ\x04\x08\n\x10\x0b\x42,\n\x1c\x63om.snowflake.apps.streamlitB\x0c\x42\x61\x63kMsgProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.BackMsg_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\014BackMsgProto'
  _globals['_BACKMSG']._serialized_start=99
  _globals['_BACKMSG']._serialized_end=469
# @@protoc_insertion_point(module_scope)
