# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .beta_text_block_param import <PERSON>TextBlockParam
from .beta_image_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON>Param
from .beta_thinking_block_param import <PERSON><PERSON><PERSON>king<PERSON><PERSON><PERSON>aram
from .beta_tool_use_block_param import <PERSON><PERSON>ool<PERSON>se<PERSON>lockParam
from .beta_base64_pdf_block_param import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Param
from .beta_tool_result_block_param import BetaToolResult<PERSON>lockParam
from .beta_mcp_tool_use_block_param import <PERSON>MCP<PERSON>ool<PERSON>se<PERSON>lockParam
from .beta_server_tool_use_block_param import <PERSON>S<PERSON>r<PERSON><PERSON><PERSON>se<PERSON>lockParam
from .beta_container_upload_block_param import <PERSON>ContainerUpload<PERSON>lockParam
from .beta_redacted_thinking_block_param import BetaRedactedThinkingBlockParam
from .beta_web_search_tool_result_block_param import BetaWebSearchToolResultBlockParam
from .beta_request_mcp_tool_result_block_param import <PERSON>RequestMCP<PERSON>oolResultBlockParam
from .beta_code_execution_tool_result_block_param import BetaCodeExecutionToolResultBlockParam

__all__ = ["BetaContentBlockParam"]

BetaContentBlockParam: TypeAlias = Union[
    BetaServerToolUseBlockParam,
    BetaWebSearchToolResultBlockParam,
    BetaCodeExecutionToolResultBlockParam,
    BetaMCPToolUseBlockParam,
    BetaRequestMCPToolResultBlockParam,
    BetaTextBlockParam,
    BetaImageBlockParam,
    BetaToolUseBlockParam,
    BetaToolResultBlockParam,
    BetaBase64PDFBlockParam,
    BetaThinkingBlockParam,
    BetaRedactedThinkingBlockParam,
    BetaContainerUploadBlockParam,
]
