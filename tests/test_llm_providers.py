import pytest
import json
from unittest.mock import patch, MagicMock

from src.integrations.llm_provider import (
    SimulatorProvider,
    OpenAIProvider,
    AnthropicProvider,
    OllamaProvider,
    create_llm_provider,
    get_available_providers,
    ProviderType
)
from src.config.llm_config import LLMConfig

class TestSimulatorProvider:
    """Test the simulator provider"""
    
    def test_initialization(self):
        """Test provider initialization"""
        provider = SimulatorProvider()
        assert provider.provider_type == ProviderType.SIMULATOR
        assert provider.is_available() == True
    
    def test_generate_text_outline(self, simulator_provider):
        """Test text generation for outline"""
        prompt = "Crie um outline detalhado para um artigo sobre Inteligência Artificial"
        result = simulator_provider.generate_text(prompt)
        
        assert isinstance(result, str)
        assert len(result) > 100
        assert "outline" in result.lower() or "introdução" in result.lower()
    
    def test_generate_text_content(self, simulator_provider):
        """Test text generation for content"""
        prompt = "Escreva um artigo de blog sobre Inteligência Artificial"
        result = simulator_provider.generate_text(prompt)
        
        assert isinstance(result, str)
        assert len(result) > 500
        assert "inteligência artificial" in result.lower()
    
    def test_generate_json_seo(self, simulator_provider):
        """Test JSON generation for SEO"""
        prompt = "Analise este conteúdo para SEO: Artigo sobre IA"
        result = simulator_provider.generate_json(prompt)
        
        assert isinstance(result, dict)
        assert "primary_keyword" in result
        assert "meta_description" in result

class TestLLMProviderFactory:
    """Test the LLM provider factory"""
    
    def test_create_simulator_provider(self):
        """Test creating simulator provider"""
        provider = create_llm_provider("simulator")
        assert isinstance(provider, SimulatorProvider)
    
    def test_create_invalid_provider(self):
        """Test creating invalid provider raises error"""
        with pytest.raises(ValueError, match="Unknown provider type"):
            create_llm_provider("invalid_provider")
    
    def test_get_available_providers(self):
        """Test getting available providers"""
        providers = get_available_providers()
        assert isinstance(providers, list)
        assert "simulator" in providers  # Simulator should always be available

class TestOpenAIProvider:
    """Test OpenAI provider (mocked)"""
    
    @patch('src.integrations.llm_provider.openai')
    def test_openai_availability_with_key(self, mock_openai):
        """Test OpenAI availability with API key"""
        provider = OpenAIProvider(api_key="test-key")
        assert provider.is_available() == True
    
    def test_openai_availability_without_key(self):
        """Test OpenAI availability without API key"""
        provider = OpenAIProvider(api_key=None)
        assert provider.is_available() == False
    
    @patch('src.integrations.llm_provider.openai')
    def test_openai_generate_text(self, mock_openai):
        """Test OpenAI text generation"""
        # Setup mock
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "Generated text"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.OpenAI.return_value = mock_client
        
        provider = OpenAIProvider(api_key="test-key")
        result = provider.generate_text("Test prompt")
        
        assert result == "Generated text"
        mock_client.chat.completions.create.assert_called_once()

class TestAnthropicProvider:
    """Test Anthropic provider (mocked)"""
    
    @patch('src.integrations.llm_provider.anthropic')
    def test_anthropic_availability_with_key(self, mock_anthropic):
        """Test Anthropic availability with API key"""
        provider = AnthropicProvider(api_key="test-key")
        assert provider.is_available() == True
    
    def test_anthropic_availability_without_key(self):
        """Test Anthropic availability without API key"""
        provider = AnthropicProvider(api_key=None)
        assert provider.is_available() == False

class TestOllamaProvider:
    """Test Ollama provider (mocked)"""
    
    @patch('src.integrations.llm_provider.requests')
    def test_ollama_availability_success(self, mock_requests):
        """Test Ollama availability when server is running"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_requests.get.return_value = mock_response
        
        provider = OllamaProvider()
        assert provider.is_available() == True
    
    @patch('src.integrations.llm_provider.requests')
    def test_ollama_availability_failure(self, mock_requests):
        """Test Ollama availability when server is down"""
        mock_requests.get.side_effect = Exception("Connection failed")
        
        provider = OllamaProvider()
        assert provider.is_available() == False
