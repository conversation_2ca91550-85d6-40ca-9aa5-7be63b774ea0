import pytest
from unittest.mock import patch, MagicMock
from src.agents.content_agents import (
    topic_researcher_agent,
    outline_generator_agent,
    content_writer_agent,
    seo_optimizer_agent,
    get_llm_provider,
    set_llm_provider
)
from src.models.workflow_state import (
    BlogWorkflowState,
    NodeNames,
    ReviewStatus,
    ReviewFeedback
)
from src.integrations.llm_provider import SimulatorProvider

class TestTopicResearcherAgent:
    """Test topic researcher agent"""
    
    def test_topic_researcher_with_existing_topic(self, initial_state):
        """Test when topic already exists in state"""
        state_with_topic = {
            **initial_state,
            "topic": "Existing Topic"
        }
        
        result = topic_researcher_agent(state_with_topic)
        
        assert result["topic"] == "Existing Topic"
        assert result["current_step"] == NodeNames.OUTLINE_GENERATOR.value
        assert "workflow_metadata" in result
    
    @patch('builtins.input', return_value='Custom Topic')
    def test_topic_researcher_with_custom_input(self, mock_input, empty_state):
        """Test with custom topic input"""
        result = topic_researcher_agent(empty_state)
        
        assert result["topic"] == "Custom Topic"
        assert result["current_step"] == NodeNames.OUTLINE_GENERATOR.value
        assert result["revision_count"] == 0
        assert result["human_approved"] == False
    
    @patch('builtins.input', return_value='')
    def test_topic_researcher_with_default_topic(self, mock_input, empty_state):
        """Test with default topic when no input provided"""
        result = topic_researcher_agent(empty_state)
        
        assert result["topic"] == "O Futuro da Inteligência Artificial em 2025"
        assert result["current_step"] == NodeNames.OUTLINE_GENERATOR.value
    
    @patch('builtins.input', return_value='Hi')
    def test_topic_researcher_with_short_topic(self, mock_input, empty_state):
        """Test with topic that's too short"""
        result = topic_researcher_agent(empty_state)
        
        assert "error_message" in result
        assert result["current_step"] == "error"

class TestOutline
