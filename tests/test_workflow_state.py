import pytest
from src.models.workflow_state import (
    BlogWorkflowState,
    NodeNames,
    ReviewStatus,
    ReviewFeedback,
    create_initial_state,
    validate_state,
    calculate_reading_time
)

class TestWorkflowState:
    """Test workflow state functions"""
    
    def test_create_initial_state(self, sample_topic):
        """Test creating initial state"""
        state = create_initial_state(sample_topic)
        
        assert state["topic"] == sample_topic
        assert state["current_step"] == NodeNames.TOPIC_RESEARCHER.value
        assert state["revision_count"] == 0
        assert state["human_approved"] == False
        assert isinstance(state["workflow_metadata"], dict)
    
    def test_validate_state_valid(self, initial_state):
        """Test validating a valid state"""
        assert validate_state(initial_state) == True
    
    def test_validate_state_invalid(self):
        """Test validating an invalid state"""
        invalid_state = BlogWorkflowState()  # Missing required fields
        assert validate_state(invalid_state) == False
    
    def test_calculate_reading_time(self, sample_content):
        """Test reading time calculation"""
        reading_time = calculate_reading_time(sample_content)
        assert isinstance(reading_time, int)
        assert reading_time > 0
    
    def test_calculate_reading_time_empty(self):
        """Test reading time with empty content"""
        reading_time = calculate_reading_time("")
        assert reading_time == 0

class TestReviewFeedback:
    """Test ReviewFeedback class"""
    
    def test_feedback_creation(self):
        """Test creating review feedback"""
        feedback = ReviewFeedback(
            status=ReviewStatus.APPROVED,
            comments="Good work",
            suggestions=["Add more examples"]
        )
        
        assert feedback.status == ReviewStatus.APPROVED
        assert feedback.comments == "Good work"
        assert len(feedback.suggestions) == 1
    
    def test_feedback_to_dict(self):
        """Test converting feedback to dictionary"""
        feedback = ReviewFeedback(
            status=ReviewStatus.NEEDS_REVISION,
            comments="Needs improvement"
        )
        
        feedback_dict = feedback.to_dict()
        assert feedback_dict["status"] == "needs_revision"
        assert feedback_dict["comments"] == "Needs improvement"
        assert isinstance(feedback_dict, dict)

class TestNodeNames:
    """Test NodeNames enum"""
    
    def test_node_names_values(self):
        """Test node name values"""
        assert NodeNames.TOPIC_RESEARCHER.value == "topic_researcher"
        assert NodeNames.OUTLINE_GENERATOR.value == "outline_generator"
        assert NodeNames.CONTENT_WRITER.value == "content_writer"
        assert NodeNames.HUMAN_REVIEW_GATE
