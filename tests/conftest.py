import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.llm_config import LLMConfig
from src.integrations.llm_provider import SimulatorProvider, create_llm_provider
from src.blog_workflow import WorkflowConfig, BlogWorkflowManager
from src.models.workflow_state import create_initial_state, ReviewFeedback, ReviewStatus

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture
def sample_topic():
    """Sample topic for testing"""
    return "Inteligência Artificial no Futuro"

@pytest.fixture
def llm_config():
    """Default LLM configuration for testing"""
    return LLMConfig(
        provider_type="simulator",
        model="test-model",
        temperature=0.7,
        max_tokens=1000
    )

@pytest.fixture
def simulator_provider():
    """Simulator provider for testing"""
    return SimulatorProvider()

@pytest.fixture
def workflow_config(temp_dir):
    """Workflow configuration for testing"""
    return WorkflowConfig(
        max_revisions=2,
        memory_path=f"{temp_dir}/test_workflow.db",
        content_preview_length=200
    )

@pytest.fixture
def workflow_manager(workflow_config):
    """Workflow manager instance for testing"""
    return BlogWorkflowManager(workflow_config)

@pytest.fixture
def initial_state(sample_topic):
    """Initial workflow state"""
    return create_initial_state(sample_topic)

@pytest.fixture
def mock_review_feedback():
    """Mock review feedback for testing"""
    return ReviewFeedback(
        status=ReviewStatus.APPROVED,
        comments="Test feedback",
        suggestions=["Test suggestion 1", "Test suggestion 2"]
    )

@pytest.fixture
def sample_outline():
    """Sample outline for testing"""
    return """
# OUTLINE: Inteligência Artificial no Futuro

## 1. Introdução (300-400 palavras)
- Hook inicial sobre IA
- Contextualização do tema
- Overview dos pontos principais

## 2. Estado Atual da IA (400-500 palavras)
- Tecnologias atuais
- Aplicações práticas
- Limitações existentes

## 3. Tendências Futuras (500-600 palavras)
- Previsões para próximos anos
- Tecnologias emergentes
- Impacto social e econômico

## 4. Conclusão (200-300 palavras)
- Resumo dos pontos principais
- Call-to-action
"""

@pytest.fixture
def sample_content():
    """Sample content for testing"""
    return """
# Inteligência Artificial no Futuro

## Introdução

A inteligência artificial representa uma das transformações mais significativas da nossa era digital. Este tema tem ganhado destaque crescente entre profissionais e organizações que buscam se manter competitivas no mercado atual.

## Estado Atual da IA

Atualmente, a IA está sendo aplicada em diversos setores, desde saúde até finanças. As tecnologias de machine learning e deep learning têm permitido avanços significativos em áreas como reconhecimento de imagens, processamento de linguagem natural e automação de processos.

## Tendências Futuras

O futuro da IA promete ainda mais inovações. Esperamos ver avanços em IA generativa, computação quântica aplicada à IA, e integração mais profunda com IoT e edge computing.

## Conclusão

A inteligência artificial continuará sendo um fator transformador nos próximos anos. É essencial que empresas e profissionais se preparem para essa revolução tecnológica.
"""

# Logging configuration for tests
import logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise in tests
