import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock
from src.blog_workflow import (
    BlogWorkflowManager,
    WorkflowConfig,
    create_persistent_workflow_manager,
    WorkflowExecutionError,
    MaxRevisionsExceededError
)
from src.models.workflow_state import (
    ReviewStatus,
    ReviewFeedback,
    NodeNames
)
from src.integrations.llm_provider import SimulatorProvider
from src.agents.content_agents import set_llm_provider

class TestFullWorkflowIntegration:
    """Test complete workflow integration"""
    
    def test_complete_workflow_with_approval(self, simulator_provider):
        """Test complete workflow from start to finish with human approval"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig(
            max_revisions=3,
            content_preview_length=200
        )
        workflow = BlogWorkflowManager(config)
        
        # Mock user inputs
        with patch('builtins.input', side_effect=['Test Topic', 'a']):  # topic, approve
            result = workflow.run_workflow("test_session")
        
        assert result["human_approved"] == True
        assert "final_post" in result
        assert result["current_step"] == "completed"
        assert "seo_metadata" in result
    
    def test_complete_workflow_with_revision(self, simulator_provider):
        """Test complete workflow with one revision cycle"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig(max_revisions=3)
        workflow = BlogWorkflowManager(config)
        
        # Mock user inputs: topic, reject, feedback, approve
        with patch('builtins.input', side_effect=[
            'Test Topic',  # topic
            'r',          # reject
            'Please add more examples',  # feedback
            'a'           # approve after revision
        ]):
            result = workflow.run_workflow("test_session")
        
        assert result["human_approved"] == True
        assert result["revision_count"] == 1
        assert "final_post" in result
    
    def test_workflow_max_revisions_exceeded(self, simulator_provider):
        """Test workflow with maximum revisions exceeded"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig(max_revisions=2)
        workflow = BlogWorkflowManager(config)
        
        # Mock user inputs: always reject
        with patch('builtins.input', side_effect=[
            'Test Topic',  # topic
            'r', 'Feedback 1',  # reject 1
            'r', 'Feedback 2',  # reject 2
            'r', 'Feedback 3'   # reject 3 (should fail)
        ]):
            with pytest.raises(MaxRevisionsExceededError):
                workflow.run_workflow("test_session")
    
    def test_workflow_with_persistent_storage(self, simulator_provider):
        """Test workflow with persistent database storage"""
        set_llm_provider(simulator_provider)
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        try:
            workflow = create_persistent_workflow_manager(
                db_path=db_path,
                max_revisions=3
            )
            
            # Start workflow
            with patch('builtins.input', side_effect=['Persistent Topic', 'a']):
                result = workflow.run_workflow("persistent_session")
            
            assert result["human_approved"] == True
            assert os.path.exists(db_path)
            
            # Test status retrieval
            status = workflow.get_workflow_status("persistent_session")
            assert status.topic == "Persistent Topic"
            assert status.human_approved == True
            
        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    def test_workflow_resume_from_checkpoint(self, simulator_provider):
        """Test resuming workflow from a checkpoint"""
        set_llm_provider(simulator_provider)
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        try:
            # First run - stop at review
            workflow1 = create_persistent_workflow_manager(db_path=db_path)
            
            with patch('builtins.input', side_effect=['Resume Topic']):
                # This should stop at human review
                try:
                    workflow1.run_workflow("resume_session")
                except Exception:
                    pass  # Expected to stop at review
            
            # Second run - resume and complete
            workflow2 = create_persistent_workflow_manager(db_path=db_path)
            
            with patch('builtins.input', side_effect=['a']):  # approve
                result = workflow2.run_workflow("resume_session")
            
            assert result["human_approved"] == True
            
        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)

class TestWorkflowErrorHandling:
    """Test workflow error handling and edge cases"""
    
    def test_workflow_with_agent_error(self, simulator_provider):
        """Test workflow handles agent errors gracefully"""
        # Create a mock provider that fails
        mock_provider = MagicMock()
        mock_provider.generate_text.side_effect = Exception("LLM Error")
        set_llm_provider(mock_provider)
        
        config = WorkflowConfig()
        workflow = BlogWorkflowManager(config)
        
        with patch('builtins.input', return_value='Test Topic'):
            with pytest.raises(WorkflowExecutionError):
                workflow.run_workflow("error_session")
    
    def test_workflow_with_invalid_config(self):
        """Test workflow with invalid configuration"""
        with pytest.raises(ValueError):
            WorkflowConfig(max_revisions=-1)
        
        with pytest.raises(ValueError):
            WorkflowConfig(content_preview_length=-1)
    
    def test_workflow_state_validation(self, simulator_provider):
        """Test workflow validates state consistency"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig()
        workflow = BlogWorkflowManager(config)
        
        # Test with empty session_id
        with pytest.raises(ValueError):
            workflow.run_workflow("")
        
        # Test with None session_id
        with pytest.raises(ValueError):
            workflow.run_workflow(None)

class TestWorkflowConfiguration:
    """Test workflow configuration options"""
    
    def test_custom_workflow_config(self, simulator_provider):
        """Test workflow with custom configuration"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig(
            max_revisions=5,
            memory_path=":memory:",
            default_thread_id="custom_thread",
            content_preview_length=100
        )
        workflow = BlogWorkflowManager(config)
        
        assert workflow.config.max_revisions == 5
        assert workflow.config.content_preview_length == 100
    
    def test_workflow_config_validation(self):
        """Test workflow config validation"""
        # Valid config
        config = WorkflowConfig(max_revisions=3)
        assert config.max_revisions == 3
        
        # Invalid max_revisions
        with pytest.raises(ValueError):
            WorkflowConfig(max_revisions=0)
        
        # Invalid content_preview_length
        with pytest.raises(ValueError):
            WorkflowConfig(content_preview_length=0)

class TestWorkflowStatus:
    """Test workflow status and monitoring"""
    
    def test_workflow_status_tracking(self, simulator_provider):
        """Test workflow status is properly tracked"""
        set_llm_provider(simulator_provider)
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        try:
            workflow = create_persistent_workflow_manager(db_path=db_path)
            
            # Check initial status (should be None)
            status = workflow.get_workflow_status("status_session")
            assert status is None
            
            # Start workflow
            with patch('builtins.input', side_effect=['Status Topic', 'a']):
                result = workflow.run_workflow("status_session")
            
            # Check final status
            status = workflow.get_workflow_status("status_session")
            assert status is not None
            assert status.topic == "Status Topic"
            assert status.current_step == "completed"
            assert status.human_approved == True
            assert status.revision_count == 0
            
        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    def test_workflow_status_during_execution(self, simulator_provider):
        """Test workflow status during execution"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig()
        workflow = BlogWorkflowManager(config)
        
        # This test would require more complex mocking to check intermediate states
        # For now, we verify that status tracking works at completion
        with patch('builtins.input', side_effect=['In Progress Topic', 'a']):
            result = workflow.run_workflow("progress_session")
        
        assert result["current_step"] == "completed"

class TestWorkflowMemoryManagement:
    """Test workflow memory and state management"""
    
    def test_in_memory_workflow(self, simulator_provider):
        """Test workflow with in-memory storage"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig(memory_path=":memory:")
        workflow = BlogWorkflowManager(config)
        
        with patch('builtins.input', side_effect=['Memory Topic', 'a']):
            result = workflow.run_workflow("memory_session")
        
        assert result["human_approved"] == True
        
        # Status should still be accessible
        status = workflow.get_workflow_status("memory_session")
        assert status.topic == "Memory Topic"
    
    def test_workflow_thread_isolation(self, simulator_provider):
        """Test that different workflow sessions are isolated"""
        set_llm_provider(simulator_provider)
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        try:
            workflow = create_persistent_workflow_manager(db_path=db_path)
            
            # Run two separate workflows
            with patch('builtins.input', side_effect=['Topic 1', 'a']):
                result1 = workflow.run_workflow("session_1")
            
            with patch('builtins.input', side_effect=['Topic 2', 'a']):
                result2 = workflow.run_workflow("session_2")
            
            # Verify isolation
            status1 = workflow.get_workflow_status("session_1")
            status2 = workflow.get_workflow_status("session_2")
            
            assert status1.topic == "Topic 1"
            assert status2.topic == "Topic 2"
            assert status1.topic != status2.topic
            
        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)

class TestWorkflowEdgeCases:
    """Test workflow edge cases and boundary conditions"""
    
    def test_workflow_with_empty_feedback(self, simulator_provider):
        """Test workflow with empty revision feedback"""
        set_llm_provider(simulator_provider)
        
        config = WorkflowConfig()
        workflow = BlogWorkflowManager(config)
        
        with patch('builtins.input', side_effect=[
            'Edge Case Topic',  # topic
            'r',               # reject
            '',                # empty feedback
            'a'                # approve
        ]):
            result = workflow.run_workflow("edge_session")
        
        assert result["human_approved"] == True
        assert result["revision_count"] == 1
    
    def test_workflow_database_permissions(self, simulator_provider):
        """Test workflow with database permission issues"""
        set_llm_provider(simulator_provider)
        
        # Try to create workflow in read-only directory
        with pytest.raises((PermissionError, OSError)):
            create_persistent_workflow_manager("/root/readonly.db")
    
    def test_workflow_large_content(self, simulator_provider):
        """Test workflow with very large content generation"""
        set_llm_provider(simulator_provider)
        
        # Mock simulator to return very large content
        large_content_provider = MagicMock()
        large_content_provider.provider_type.value = "large_simulator"
        large_content_provider.generate_text.return_value = "Large content. " * 10000
        large_content_provider.generate_json.return_value = {
            "primary_keyword": "test",
            "meta_description": "test description",
            "suggested_tags": ["tag1", "tag2"]
        }
        set_llm_provider(large_content_provider)
        
        config = WorkflowConfig()
        workflow = BlogWorkflowManager(config)
        
        with patch('builtins.input', side_effect=['Large Content Topic', 'a']):
            result = workflow.run_workflow("large_session")
        
        assert result["human_approved"] == True
        assert len(result["final_post"]) > 100000  # Very large content
