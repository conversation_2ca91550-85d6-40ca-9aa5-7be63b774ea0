.PHONY: help install install-dev test lint format clean docker-build docker-run docker-stop setup demo

# Default target
help: ## Show this help message
	@echo "🚀 Blog Workflow LangGraph - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Quick Setup
setup: ## Complete project setup (recommended for first use)
	python setup_project.py

# Installation
install: ## Install the package
	pip install -e .

install-dev: ## Install with development dependencies
	pip install -e ".[dev]"

install-all: ## Install with all optional dependencies
	pip install -e ".[dev,enhanced]"
	pip install fastapi uvicorn streamlit

# LLM Setup
setup-openai: ## Setup OpenAI integration
	@echo "📋 Setting up OpenAI integration..."
	@echo "Add to .env: OPENAI_API_KEY=sk-your-key-here"

setup-anthropic: ## Setup Anthropic integration
	@echo "📋 Setting up Anthropic integration..."
	@echo "Add to .env: ANTHROPIC_API_KEY=sk-ant-your-key-here"

# Testing and Quality
test: ## Run basic tests
	python test_simple.py

test-full: ## Run full test suite
	pytest tests/ -v

test-cov: ## Run tests with coverage
	pytest tests/ --cov=src --cov-report=html

lint: ## Run code linting
	black --check src/ examples/ tests/
	isort --check-only src/ examples/ tests/

format: ## Format code
	black src/ examples/ tests/
	isort src/ examples/ tests/

# Development
clean: ## Clean build artifacts
	rm -rf build/ dist/ *.egg-info/ .pytest_cache/ .coverage htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Docker commands
docker-build: ## Build Docker image
	docker-compose build

docker-run: ## Run with Docker Compose
	docker-compose up -d

docker-stop: ## Stop Docker containers
	docker-compose down

docker-logs: ## View Docker logs
	docker-compose logs -f

# Demos
demo: ## Run interactive demo
	python examples/basic_usage.py

demo-simple: ## Run simple demo (no interaction)
	echo "n" | python examples/basic_usage.py

# API & UI
run-api: ## Run FastAPI server
	uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

run-ui: ## Run Streamlit app
	streamlit run apps/streamlit_app.py

# Quick commands
quickstart: setup demo ## Complete setup and demo
