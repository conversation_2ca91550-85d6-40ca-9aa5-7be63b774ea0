#!/usr/bin/env python3
"""
Script de configuração do Blog Workflow LangGraph
Configura o projeto para uso completo
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, description):
    """Executa um comando e mostra o resultado"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - OK")
            return True
        else:
            print(f"❌ {description} - ERRO: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} - ERRO: {e}")
        return False

def main():
    print("🚀 CONFIGURAÇÃO DO BLOG WORKFLOW LANGGRAPH")
    print("=" * 50)
    
    # Verificar se estamos no diretório correto
    if not Path("src/blog_workflow.py").exists():
        print("❌ Execute este script no diretório raiz do projeto!")
        sys.exit(1)
    
    success_count = 0
    total_steps = 6
    
    # 1. Instalar dependências básicas
    if run_command("pip install -r requirements.txt", "Instalando dependências básicas"):
        success_count += 1
    
    # 2. Instalar dependências opcionais para API/UI
    if run_command("pip install fastapi uvicorn streamlit", "Instalando dependências para API/UI"):
        success_count += 1
    
    # 3. Instalar LangGraph se disponível
    if run_command("pip install langgraph", "Instalando LangGraph (opcional)"):
        success_count += 1
    
    # 4. Executar testes básicos
    if run_command("python test_simple.py", "Executando testes básicos"):
        success_count += 1
    
    # 5. Verificar estrutura do projeto
    required_files = [
        "src/blog_workflow.py",
        "src/agents/content_agents.py",
        "src/integrations/llm_provider.py",
        "src/models/workflow_state.py"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Arquivo obrigatório não encontrado: {file_path}")
            all_files_exist = False
    
    if all_files_exist:
        print("✅ Verificação da estrutura do projeto - OK")
        success_count += 1
    
    # 6. Criar configuração de exemplo
    config_example = """# Exemplo de configuração de variáveis de ambiente
# Copie para .env e configure suas chaves de API

# OpenAI
# OPENAI_API_KEY=sua_chave_openai_aqui

# Anthropic
# ANTHROPIC_API_KEY=sua_chave_anthropic_aqui

# Ollama (se usando localmente)
# OLLAMA_BASE_URL=http://localhost:11434
"""
    
    try:
        with open(".env.example", "w") as f:
            f.write(config_example)
        print("✅ Criação do arquivo .env.example - OK")
        success_count += 1
    except Exception as e:
        print(f"❌ Criação do arquivo .env.example - ERRO: {e}")
    
    # Resultado final
    print("\n" + "=" * 50)
    print("📊 RESULTADO DA CONFIGURAÇÃO")
    print("=" * 50)
    print(f"✅ Passos concluídos: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎉 PROJETO CONFIGURADO COM SUCESSO!")
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Configure suas chaves de API no arquivo .env (opcional)")
        print("2. Execute: python examples/basic_usage.py")
        print("3. Para API: python api/main.py")
        print("4. Para UI: streamlit run apps/streamlit_app.py")
    else:
        print("⚠️  CONFIGURAÇÃO PARCIAL - Alguns passos falharam")
        print("Verifique os erros acima e tente novamente")
    
    return success_count == total_steps

if __name__ == "__main__":
    main()
