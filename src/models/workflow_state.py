import logging
from enum import Enum
from dataclasses import dataclass, field
from typing import TypedDict, List, Optional, Dict, Any, Union

logger = logging.getLogger(__name__)

# --- 1. Enums e Classes de Dados ---

class NodeNames(Enum):
    """Enum para nomes dos nós - evita strings hardcoded"""
    TOPIC_RESEARCHER = "topic_researcher"
    OUTLINE_GENERATOR = "outline_generator"
    CONTENT_WRITER = "content_writer"
    HUMAN_REVIEW_GATE = "human_review_gate"
    SEO_OPTIMIZER = "seo_optimizer"
    FINALIZER = "finalizer"

class ReviewStatus(Enum):
    """Status da revisão humana"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVISION = "needs_revision"

@dataclass
class BlogPost:
    """Classe para representar um blog post completo"""
    title: str = ""
    content: str = ""
    seo_keywords: List[str] = field(default_factory=list)
    meta_description: str = ""
    word_count: int = 0
    
    def __post_init__(self):
        """Calcula word count automaticamente"""
        if self.content and not self.word_count:
            self.word_count = len(self.content.split())

@dataclass
class ReviewFeedback:
    """Classe para feedback de revisão"""
    status: ReviewStatus
    comments: str = ""
    suggestions: List[str] = field(default_factory=list)
    reviewer_name: str = "Human Reviewer"
    timestamp: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário para serialização"""
        return {
            "status": self.status.value,
            "comments": self.comments,
            "suggestions": self.suggestions,
            "reviewer_name": self.reviewer_name,
            "timestamp": self.timestamp
        }

@dataclass
class WorkflowMetadata:
    """Metadados do workflow para tracking"""
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    topic_source: str = "manual"
    total_duration: Optional[float] = None
    steps_completed: List[str] = field(default_factory=list)
    
    def add_completed_step(self, step: str) -> None:
        """Adiciona step completado à lista"""
        if step not in self.steps_completed:
            self.steps_completed.append(step)

# --- 2. Estado do Workflow (Melhorado) ---

class BlogWorkflowState(TypedDict, total=False):
    # Conteúdo principal
    topic: str
    outline: str
    draft_content: str
    final_post: str
    
    # Revisão humana
    review_feedback: Optional[ReviewFeedback]
    human_approved: bool
    revision_count: int
    
    # SEO - pode ser string JSON ou dict
    seo_suggestions: Union[str, Dict[str, Any]]
    target_keywords: List[str]
    
    # Controle de fluxo e metadados
    current_step: str
    error_message: Optional[str]
    workflow_metadata: Dict[str, Any]
    
    # Campos adicionais para tracking
    content_length: Optional[int]
    estimated_reading_time: Optional[int]

# --- 3. Funções utilitárias ---

def calculate_reading_time(content: str, words_per_minute: int = 200) -> int:
    """Calcula tempo estimado de leitura"""
    if not content:
        return 0
    word_count = len(content.split())
    return max(1, round(word_count / words_per_minute))

def validate_state(state: BlogWorkflowState) -> bool:
    """Valida se o estado do workflow está consistente"""
    try:
        # Verifica campos obrigatórios
        required_fields = ["current_step"]
        for field in required_fields:
            if field not in state:
                logger.warning(f"Campo obrigatório ausente: {field}")
                return False
        
        # Valida revision_count
        revision_count = state.get("revision_count", 0)
        if revision_count < 0:
            logger.warning("revision_count não pode ser negativo")
            return False
        
        return True
    except Exception as e:
        logger.error(f"Erro na validação do estado: {e}")
        return False

def create_initial_state(topic: str) -> BlogWorkflowState:
    """Cria estado inicial do workflow"""
    return BlogWorkflowState(
        topic=topic,
        outline="",
        draft_content="",
        final_post="",
        review_feedback=None,
        human_approved=False,
        revision_count=0,
        seo_suggestions="",
        target_keywords=[],
        current_step=NodeNames.TOPIC_RESEARCHER.value,
        error_message=None,
        workflow_metadata={
            "start_time": None,
            "topic_source": "manual",
            "steps_completed": []
        },
        content_length=None,
        estimated_reading_time=None
    )