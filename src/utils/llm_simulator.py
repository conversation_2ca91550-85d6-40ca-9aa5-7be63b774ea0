import logging
import json
import re
from typing import Optional, Dict, Any, List
from datetime import datetime

# Relative import for ReviewFeedback and ReviewStatus
from ..models.workflow_state import ReviewFeedback, ReviewStatus

# Add missing import at the top
from ..integrations.llm_provider import BaseLLMProvider

logger = logging.getLogger(__name__)

class LLMSimulator:
    """Simulador de chamadas LLM com templates. (Classe auxiliar, não o provedor)"""
    
    # Templates de conteúdo por categoria
    CONTENT_TEMPLATES = {
        "technology": {
            "intro_hooks": [
                "A revolução digital está transformando",
                "Em 2025, a tecnologia de",
                "O futuro da inovação depende de"
            ],
            "sections": [
                "Fundamentos Tecnológicos",
                "Implementação Prática",
                "Casos de Uso Reais",
                "Desafios e Soluções"
            ]
        },
        "business": {
            "intro_hooks": [
                "O mercado está evoluindo rapidamente com",
                "Empresas líderes já adotaram",
                "A transformação digital exige"
            ],
            "sections": [
                "Análise de Mercado",
                "Estratégias de Implementação",
                "ROI e Benefícios",
                "Tendências Futuras"
            ]
        }
    }
    
    @classmethod
    def _detect_content_category(cls, topic: str) -> str:
        """Detecta categoria do conteúdo baseado no tópico"""
        tech_keywords = ["ai", "inteligência artificial", "tecnologia", "digital", "software", "dados"]
        business_keywords = ["negócio", "empresa", "mercado", "estratégia", "gestão"]
        
        topic_lower = topic.lower()
        
        if any(keyword in topic_lower for keyword in tech_keywords):
            return "technology"
        elif any(keyword in topic_lower for keyword in business_keywords):
            return "business"
        else:
            return "technology"  # default
    
    @staticmethod
    def generate_outline(topic: str) -> str:
        """Gera um outline mais detalhado e estruturado"""
        logger.info(f"🤖 Gerando outline para: {topic}")
        
        if not topic or len(topic.strip()) < 3:
            raise ValueError("Tópico inválido para geração de outline")
        
        category = LLMSimulator._detect_content_category(topic)
        template_data = LLMSimulator.CONTENT_TEMPLATES.get(category, LLMSimulator.CONTENT_TEMPLATES["technology"])
        
        # Calcula estimativa de palavras
        estimated_words = 2500 + (len(topic) * 10)  # Base + complexidade do tópico
        
        outline_template = f"""
# OUTLINE: {topic}

## 1. Introdução (300-400 palavras)
- Hook inicial: {template_data['intro_hooks'][0]} {topic.lower()}
- Contextualização do tema e sua relevância atual
- Overview dos pontos principais que serão abordados
- Por que este tópico é crucial em 2025

## 2. Contexto e Background (400-500 palavras)
- Situação atual e evolução histórica de {topic.lower()}
- Principais drivers de mudança e fatores influenciadores
- Análise do cenário competitivo atual
- Desafios e oportunidades emergentes

## 3. {template_data['sections'][0]} (500-600 palavras)
- Conceitos fundamentais e definições
- Arquiteturas e metodologias principais
- Componentes essenciais para implementação
- Exemplos práticos e casos de referência

## 4. {template_data['sections'][1]} (400-500 palavras)
- Guia step-by-step para implementação
- Ferramentas e tecnologias recomendadas
- Melhores práticas da indústria
- Considerações de segurança e compliance

## 5. {template_data['sections'][2]} (600-700 palavras)
- Cases de sucesso e lições aprendidas
- Análise de ROI e métricas de sucesso
- Comparação de diferentes abordagens
- Testimonials e insights de especialistas

## 6. {template_data['sections'][3]} (300-400 palavras)
- Principais obstáculos e como superá-los
- Estratégias de mitigação de riscos
- Soluções inovadoras e alternativas
- Roadmap para resolução de problemas

## 7. Tendências Futuras e Próximos Passos (300-400 palavras)
- Projeções para os próximos 2-3 anos
- Tecnologias emergentes relacionadas
- Oportunidades de inovação
- Call-to-action e recursos adicionais

**Meta-informações:**
- Palavras-alvo: {estimated_words}-{estimated_words + 500} palavras
- Tom: Autoritativo e informativo
- Público: Profissionais e tomadores de decisão
- Categoria: {category.title()}
- Palavras-chave sugeridas: {', '.join(topic.lower().split()[:3])}
        """
        
        logger.info(f"Outline gerado para categoria '{category}' - ~{estimated_words} palavras estimadas")
        return outline_template.strip()
    
    @staticmethod
    def write_content(outline: str, topic: str, feedback: Optional[ReviewFeedback] = None) -> str:
        """Gera conteúdo baseado no outline e feedback com melhor estrutura"""
        logger.info("✍️ Gerando conteúdo do blog post")
        
        if not outline or not topic:
            raise ValueError("Outline e tópico são obrigatórios")
        
        # Processa feedback se existir
        feedback_context = ""
        feedback_adjustments = ""
        
        if feedback and feedback.status == ReviewStatus.NEEDS_REVISION:
            feedback_context = f"\n[INCORPORANDO FEEDBACK: {feedback.comments}]"
            
            # Ajustes baseados no tipo de feedback
            if "mais detalhes" in feedback.comments.lower():
                feedback_adjustments = "\n*[Versão expandida com mais detalhes técnicos]*"
            elif "mais simples" in feedback.comments.lower():
                feedback_adjustments = "\n*[Versão simplificada para melhor compreensão]*"
            elif "exemplos" in feedback.comments.lower():
                feedback_adjustments = "\n*[Versão com exemplos práticos adicionais]*"
        
        # Detecta categoria para personalizar conteúdo
        category = LLMSimulator._detect_content_category(topic)
        
        # Gera introdução dinâmica
        intro_hook = LLMSimulator.CONTENT_TEMPLATES[category]["intro_hooks"][0]
        
        content = f"""
# {topic}

{feedback_adjustments}

## Introdução

{intro_hook} {topic.lower()} representa uma das transformações mais significativas da nossa era digital. Este tema tem ganhado destaque crescente entre profissionais e organizações que buscam se manter competitivas no mercado atual.

Neste artigo, exploramos as nuances, desafios e oportunidades que {topic.lower()} apresenta para diferentes setores da economia. Abordaremos desde os fundamentos técnicos até estratégias práticas de implementação.

{feedback_context}

## Contexto e Evolução Atual

O cenário atual mostra uma evolução acelerada nas tecnologias e metodologias relacionadas a {topic.lower()}. Empresas pioneiras já estão colhendo os frutos de implementações bem-sucedidas, enquanto outras ainda avaliam o melhor momento e abordagem para adoção.

### Principais Tendências Observadas:

- **Automatização Inteligente:** Processos complexos sendo otimizados através de soluções inovadoras
- **Integração Sistêmica:** Conexão fluida com infraestruturas e sistemas existentes
- **Experiência Centrada no Usuário:** Foco na simplicidade e usabilidade
- **Governança e Compliance:** Atenção crescente a aspectos éticos, legais e de privacidade

### Fatores Impulsionadores:

1. **Pressão Competitiva:** Necessidade de diferenciação no mercado
2. **Demanda dos Consumidores:** Expectativas elevadas por soluções mais eficientes
3. **Avanços Tecnológicos:** Disponibilidade de ferramentas mais poderosas e acessíveis
4. **Regulamentações:** Novas exigências e padrões da indústria

## Fundamentos e Aspectos Técnicos

A implementação efetiva de {topic.lower()} requer compreensão sólida de conceitos fundamentais e componentes técnicos essenciais.

### Arquitetura Base:

**1. Camada de Dados:**
- Estruturas de armazenamento otimizadas
- Pipelines de processamento eficientes
- Garantias de integridade e segurança

**2. Camada de Processamento:**
- Algoritmos otimizados para performance
- Capacidades de escalonamento horizontal
- Tolerância a falhas e recuperação automática

**3. Camada de Interface:**
- APIs RESTful bem documentadas
- Interfaces de usuário intuitivas
- Dashboards de monitoramento em tempo real

**4. Camada de Segurança:**
- Autenticação multi-fator
- Criptografia end-to-end
- Auditoria completa de atividades

### Considerações de Design:

- **Escalabilidade:** Capacidade de crescer

    @staticmethod
    def optimize_seo(content: str, topic: str) -> Dict[str, Any]:
        """Otimiza conteúdo para SEO com análise mais detalhada"""
        logger.info("🔍 Gerando otimizações de SEO")
        
        if not content or not topic:
            raise ValueError("Conteúdo e tópico são obrigatórios para otimização de SEO")
        
        # Análise básica do conteúdo
        word_count = len(content.split())
        sentences = len([s for s in content.split('.') if s.strip()])
        
        # Extrai palavras-chave principais
        topic_words = [word.lower() for word in topic.split() if len(word) > 2]
        
        # Gera sugestões de SEO mais realistas
        seo_data = {
            "primary_keyword": topic.lower(),
            "secondary_keywords": topic_words[:5],
            
            "meta_description": f"Descubra tudo sobre {topic.lower()} neste guia completo. "
                              f"Análise detalhada, estratégias práticas e insights atualizados para 2025.",
            
            "title_suggestions": [
                f"{topic}: Guia Completo 2025",
                f"Como Implementar {topic} na Sua Empresa",
                f"{topic}: Estratégias e Melhores Práticas",
                f"O Futuro de {topic}: Tendências e Oportunidades"
            ],
            
            "content_improvements": [
                "Adicionar mais subtítulos (H2/H3) para melhor estruturação",
                "Incluir lista com bullets para melhor escaneabilidade",
                "Adicionar call-to-action no final do artigo",
                f"Garantir densidade da palavra-chave '{topic.lower()}' entre 1-2%",
                "Incluir links internos para outros artigos relacionados",
                "Adicionar imagens com alt-text otimizado"
            ],
            
            "technical_seo": {
                "recommended_length": "2500-3500 palavras",
                "current_length": f"{word_count} palavras",
                "readability_score": "Intermediário",
                "keyword_placement": [
                    "Título principal (H1)",
                    "Primeiro parágrafo",
                    "Pelo menos 2 subtítulos",
                    "Meta description",
                    "URL slug"
                ]
            },
            
            "social_media": {
                "facebook_title": f"{topic}: Transforme Seu Negócio em 2025",
                "twitter_description": f"Guia essencial sobre {topic.lower()} - estratégias, tendências e implementação prática.",
                "linkedin_hook": f"Por que {topic.lower()} é fundamental para o sucesso empresarial?"
            },
            
            "performance_metrics": {
                "estimated_reading_time": f"{max(1, word_count // 200)} minutos",
                "content_score": min(100, max(60, word_count // 25)),
                "seo_readiness": "Bom" if word_count > 1500 else "Necessita melhorias"
            }
        }
        
        logger.info(f"SEO otimizado - Score: {seo_data['performance_metrics']['content_score']}/100")
        return seo_data
    
    @staticmethod
    def finalize_content(draft_content: str, seo_data: Dict[str, Any], topic: str) -> str:
        """Finaliza o conteúdo aplicando otimizações de SEO"""
        logger.info("🎯 Finalizando conteúdo com otimizações de SEO")
        
        if not draft_content:
            raise ValueError("Conteúdo de rascunho não encontrado")
        
        # Extrai dados de SEO
        meta_description = seo_data.get("meta_description", "")
        primary_keyword = seo_data.get("primary_keyword", topic.lower())
        title_suggestions = seo_data.get("title_suggestions", [])
        
        # Seleciona melhor título
        final_title = title_suggestions[0] if title_suggestions else topic
        
        # Monta conteúdo final otimizado
        final_content = f"""
# {final_title}

*Tempo de leitura estimado: {seo_data.get('performance_metrics', {}).get('estimated_reading_time', '5 minutos')}*

{draft_content}

---

## Conclusão

{primary_keyword.title()} representa uma oportunidade fundamental para empresas que buscam inovação e crescimento sustentável. A implementação das estratégias apresentadas pode transformar significativamente os resultados organizacionais.

### Próximos Passos Recomendados:

1. **Avaliação Inicial**: Analise sua situação atual e identifique gaps
2. **Planejamento Estratégico**: Desenvolva um roadmap de implementação
3. **Execução Gradual**: Implemente mudanças de forma iterativa
4. **Monitoramento**: Acompanhe métricas e ajuste estratégias

### Recursos Adicionais:

- **Palavra-chave principal**: {primary_keyword}
- **Palavras-chave relacionadas**: {', '.join(seo_data.get('secondary_keywords', []))}
- **Meta-descrição**: {meta_description}

---

*Este artigo foi otimizado para SEO e atualizado com as melhores práticas de {datetime.now().year}.*
        """
        
        logger.info("Conteúdo finalizado com otimizações aplicadas")
        return final_content.strip()

class LLMSimulatorProvider(BaseLLMProvider): # Herda de BaseLLMProvider
    """Implementação do provedor LLM usando o simulador existente."""
    
    def __init__(self, **kwargs): # Aceita kwargs para compatibilidade
        super().__init__(**kwargs)
        logger.info("Provedor LLM Simulador inicializado.")
    
    def _get_provider_type(self):
        from ..integrations.llm_provider import ProviderType
        return ProviderType.SIMULATOR
    
    def generate_text(self, prompt: str, **kwargs) -> str:
        """Gera texto usando o simulador, analisando o prompt."""
        logger.debug(f"Simulador: generate_text para prompt: {prompt[:100]}...")
        
        # Lógica de roteamento baseada no conteúdo do prompt
        if "outline" in prompt.lower() or "estrutura" in prompt.lower() or "crie um outline" in prompt.lower():
            topic = self._extract_topic_from_prompt(prompt, "outline")
            return LLMSimulator.generate_outline(topic)
        
        elif "escreva um artigo de blog" in prompt.lower() or "write content" in prompt.lower():
            topic = self._extract_topic_from_prompt(prompt, "content")
            # Tenta extrair outline do prompt, se não, usa um genérico
            outline_marker = "Use este outline como estrutura:"
            outline_content = ""
            if outline_marker in prompt:
                outline_content = prompt.split(outline_marker, 1)[1].split("\n\n", 1)[0].strip()
            
            if not outline_content: # Fallback se não encontrar outline explícito
                outline_content = LLMSimulator.generate_outline(topic)

            # Tenta extrair feedback
            feedback_marker = "FEEDBACK PARA INCORPORAR:"
            feedback_obj = None
            if feedback_marker in prompt:
                feedback_text = prompt.split(feedback_marker,1)[1].split("\n\n",1)[0].strip()
                # Simula um objeto ReviewFeedback simples
                from ..models.workflow_state import ReviewStatus, ReviewFeedback # Import local
                feedback_obj = ReviewFeedback(status=ReviewStatus.NEEDS_REVISION, comments=feedback_text)

            return LLMSimulator.write_content(outline_content, topic, feedback_obj)
        
        else: # Fallback genérico se não identificar o tipo de prompt
            logger.warning("Simulador: Tipo de prompt não identificado claramente para generate_text. Retornando texto genérico.")
            return f"Texto simulado para o tópico: {self._extract_topic_from_prompt(prompt, 'generic')}. Conteúdo detalhado sobre o assunto."

    def generate_json(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Gera JSON usando o simulador, focado em SEO."""
        logger.debug(f"Simulador: generate_json para prompt: {prompt[:100]}...")
        
        # Assume que generate_json é usado principalmente para SEO no simulador
        if "seo" in prompt.lower() or "otimiza" in prompt.lower():
            topic = self._extract_topic_from_prompt(prompt, "seo")
            # Tenta extrair conteúdo do prompt para SEO
            content_marker = "CONTEÚDO (primeiros " # Ex: "CONTEÚDO (primeiros 3000 caracteres):"
            content_for_seo = f"Conteúdo simulado sobre {topic} para análise de SEO." # Default
            if content_marker in prompt:
                 # Pega o que vem depois de "CONTEÚDO (...):" e antes de "Forneça as seguintes..."
                temp_content = prompt.split(content_marker, 1)[1]
                # Remove a parte numérica e os dois pontos
                temp_content = temp_content.split("):",1)[1] if "):" in temp_content else temp_content
                content_for_seo = temp_content.split("Forneça as seguintes informações em formato JSON válido:",1)[0].strip()


            return LLMSimulator.optimize_seo(content_for_seo, topic)
        else:
            logger.warning("Simulador: Tipo de prompt não identificado para generate_json. Retornando JSON genérico.")
            return {"simulated_data": True, "topic": self._extract_topic_from_prompt(prompt, "generic_json"), "message": "JSON simulado."}

    def _extract_topic_from_prompt(self, prompt: str, context: str = "") -> str:
        """Tenta extrair o tópico do prompt de forma mais robusta."""
        # Procura por padrões como "TÓPICO: X", "sobre: X", "topic: X"
        patterns = [
            r"TÓPICO:\s*\"([^\"]+)\"", r"TÓPICO:\s*([^\n]+)",
            r"sobre:\s*\"([^\"]+)\"", r"sobre:\s*([^\n]+)",
            r"topic:\s*\"([^\"]+)\"", r"topic:\s*([^\n]+)",
            r"blog post sobre\s*\"([^\"]+)\"", r"artigo de blog sobre\s*\"([^\"]+)\"",
            r"blog post sobre\s*([^\n]+)", r"artigo de blog sobre\s*([^\n]+)"
        ]
        for pattern in patterns:
            match = re.search(pattern, prompt, re.IGNORECASE)
            if match:
                # Pega o primeiro grupo que não for None
                topic = next((g for g in match.groups() if g is not None), None)
                if topic:
                    logger.debug(f"Simulador ({context}): Tópico extraído '{topic.strip()}'")
                    return topic.strip()
        
        # Fallback se nenhum padrão for encontrado
        fallback_topic = prompt.split('\n')[0][:70].replace("Crie um outline detalhado e estruturado para um artigo de blog sobre", "").strip()
        logger.debug(f"Simulador ({context}): Tópico fallback '{fallback_topic}'")
        return fallback_topic
